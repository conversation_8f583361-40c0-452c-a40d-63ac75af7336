[{"level_": 0, "message_": "Start JSON generation. Platform version: 29 min SDK version: armeabi-v7a", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a/android_gradle_build.json due to:", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a'", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a'", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=29 \\\n  -DANDROID_PLATFORM=android-29 \\\n  -DANDROID_ABI=armeabi-v7a \\\n  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/cxx/Debug/673o2q1m/obj/armeabi-v7a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/cxx/Debug/673o2q1m/obj/armeabi-v7a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a \\\n  -GNinja\n", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=29 \\\n  -DANDROID_PLATFORM=android-29 \\\n  -DANDROID_ABI=armeabi-v7a \\\n  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/cxx/Debug/673o2q1m/obj/armeabi-v7a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/cxx/Debug/673o2q1m/obj/armeabi-v7a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a \\\n  -GNinja\n", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a/compile_commands.json.bin normally", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/armeabi-v7a/compile_commands.json to /Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/tools/debug/armeabi-v7a/compile_commands.json", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
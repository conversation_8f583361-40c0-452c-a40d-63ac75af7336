[{"level_": 0, "message_": "Start JSON generation. Platform version: 29 min SDK version: x86", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/work_space/PhotoCC_android/pdf/.cxx/Debug/673o2q1m/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/work_space/PhotoCC_android/pdf/src/main/cpp/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
package com.wtb.photocc_android.export

import android.content.Context
import android.net.Uri
import androidx.test.core.app.ApplicationProvider
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportFormat
import com.wtb.photocc_android.data.export.ExportTask
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import java.io.ByteArrayOutputStream

@RunWith(RobolectricTestRunner::class)
class ZipExporterTest {

    private lateinit var context: Context
    private lateinit var zipExporter: ZipExporter

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        zipExporter = ZipExporter(context)
    }

    @Test
    fun testZipExportWithEmptyImages() = runBlocking {
        val config = ExportConfig(
            format = ExportFormat.ZIP,
            fileName = "test",
            compressionLevel = 6
        )
        
        val task = ExportTask(
            config = config,
            images = emptyList()
        )
        
        val outputStream = ByteArrayOutputStream()
        
        val result = zipExporter.export(task, outputStream) { updatedTask ->
            println("Progress: ${updatedTask.progress}% - ${updatedTask.message}")
        }
        
        // 即使没有图片，也应该创建一个空的ZIP文件
        assert(result.isSuccess)
        assert(outputStream.size() > 0)
    }

    @Test
    fun testZipExportWithMockImages() = runBlocking {
        val config = ExportConfig(
            format = ExportFormat.ZIP,
            fileName = "test_images",
            compressionLevel = 3
        )
        
        // 创建模拟图片项（这些URI在测试环境中不会真正存在）
        val mockImages = listOf(
            ImageItem(
                uri = "content://mock/image1.jpg",
                name = "image1.jpg",
                size = 1024L,
                lastModified = System.currentTimeMillis()
            ),
            ImageItem(
                uri = "content://mock/image2.jpg", 
                name = "image2.jpg",
                size = 2048L,
                lastModified = System.currentTimeMillis()
            )
        )
        
        val task = ExportTask(
            config = config,
            images = mockImages
        )
        
        val outputStream = ByteArrayOutputStream()
        
        val result = zipExporter.export(task, outputStream) { updatedTask ->
            println("Progress: ${updatedTask.progress}% - ${updatedTask.message}")
        }
        
        // 由于模拟图片不存在，导出应该仍然成功但可能跳过无效图片
        println("Export result: $result")
        println("Output stream size: ${outputStream.size()}")
    }
}

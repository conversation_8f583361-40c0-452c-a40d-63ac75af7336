com.github.barteksc.pdfviewer.test:styleable/View = 0x7f10008e
com.github.barteksc.pdfviewer.test:styleable/Transition = 0x7f10008c
com.github.barteksc.pdfviewer.test:styleable/Transform = 0x7f10008b
com.github.barteksc.pdfviewer.test:styleable/Tooltip = 0x7f10008a
com.github.barteksc.pdfviewer.test:styleable/TextInputEditText = 0x7f100086
com.github.barteksc.pdfviewer.test:styleable/TextAppearance = 0x7f100085
com.github.barteksc.pdfviewer.test:styleable/TabItem = 0x7f100083
com.github.barteksc.pdfviewer.test:styleable/StateListDrawable = 0x7f10007e
com.github.barteksc.pdfviewer.test:styleable/Spinner = 0x7f10007c
com.github.barteksc.pdfviewer.test:styleable/SnackbarLayout = 0x7f10007b
com.github.barteksc.pdfviewer.test:styleable/Snackbar = 0x7f10007a
com.github.barteksc.pdfviewer.test:styleable/Slider = 0x7f100079
com.github.barteksc.pdfviewer.test:styleable/ShapeableImageView = 0x7f100077
com.github.barteksc.pdfviewer.test:styleable/RangeSlider = 0x7f10006e
com.github.barteksc.pdfviewer.test:styleable/RadialViewGroup = 0x7f10006d
com.github.barteksc.pdfviewer.test:styleable/PropertySet = 0x7f10006c
com.github.barteksc.pdfviewer.test:styleable/PopupWindowBackgroundState = 0x7f10006b
com.github.barteksc.pdfviewer.test:styleable/OnClick = 0x7f100068
com.github.barteksc.pdfviewer.test:styleable/NavigationView = 0x7f100067
com.github.barteksc.pdfviewer.test:styleable/MotionLayout = 0x7f100061
com.github.barteksc.pdfviewer.test:styleable/MockView = 0x7f10005e
com.github.barteksc.pdfviewer.test:styleable/MenuView = 0x7f10005d
com.github.barteksc.pdfviewer.test:styleable/MenuItem = 0x7f10005c
com.github.barteksc.pdfviewer.test:styleable/MaterialDivider = 0x7f100053
com.github.barteksc.pdfviewer.test:styleable/MaterialCheckBoxStates = 0x7f100052
com.github.barteksc.pdfviewer.test:styleable/MaterialCheckBox = 0x7f100051
com.github.barteksc.pdfviewer.test:styleable/MaterialCalendar = 0x7f10004e
com.github.barteksc.pdfviewer.test:styleable/MaterialButtonToggleGroup = 0x7f10004d
com.github.barteksc.pdfviewer.test:styleable/LinearProgressIndicator = 0x7f100047
com.github.barteksc.pdfviewer.test:styleable/LinearLayoutCompat = 0x7f100045
com.github.barteksc.pdfviewer.test:styleable/KeyPosition = 0x7f100041
com.github.barteksc.pdfviewer.test:styleable/KeyFramesVelocity = 0x7f100040
com.github.barteksc.pdfviewer.test:styleable/KeyFrame = 0x7f10003e
com.github.barteksc.pdfviewer.test:styleable/KeyAttribute = 0x7f10003c
com.github.barteksc.pdfviewer.test:styleable/ImageFilterView = 0x7f10003a
com.github.barteksc.pdfviewer.test:styleable/GradientColor = 0x7f100038
com.github.barteksc.pdfviewer.test:styleable/ForegroundLinearLayout = 0x7f100035
com.github.barteksc.pdfviewer.test:styleable/FontFamily = 0x7f100033
com.github.barteksc.pdfviewer.test:styleable/FloatingActionButton = 0x7f100030
com.github.barteksc.pdfviewer.test:styleable/ExtendedFloatingActionButton = 0x7f10002e
com.github.barteksc.pdfviewer.test:styleable/DrawerLayout = 0x7f10002d
com.github.barteksc.pdfviewer.test:styleable/CoordinatorLayout_Layout = 0x7f10002a
com.github.barteksc.pdfviewer.test:styleable/ConstraintSet = 0x7f100028
com.github.barteksc.pdfviewer.test:styleable/Constraint = 0x7f100025
com.github.barteksc.pdfviewer.test:styleable/CollapsingToolbarLayout_Layout = 0x7f100022
com.github.barteksc.pdfviewer.test:styleable/ClockFaceView = 0x7f10001f
com.github.barteksc.pdfviewer.test:styleable/ChipGroup = 0x7f10001d
com.github.barteksc.pdfviewer.test:styleable/Chip = 0x7f10001c
com.github.barteksc.pdfviewer.test:styleable/CheckedTextView = 0x7f10001b
com.github.barteksc.pdfviewer.test:styleable/Capability = 0x7f100019
com.github.barteksc.pdfviewer.test:styleable/BottomSheetBehavior_Layout = 0x7f100017
com.github.barteksc.pdfviewer.test:styleable/BottomAppBar = 0x7f100015
com.github.barteksc.pdfviewer.test:styleable/BaseProgressIndicator = 0x7f100014
com.github.barteksc.pdfviewer.test:styleable/Badge = 0x7f100013
com.github.barteksc.pdfviewer.test:styleable/AppCompatImageView = 0x7f10000e
com.github.barteksc.pdfviewer.test:styleable/AppBarLayout_Layout = 0x7f10000c
com.github.barteksc.pdfviewer.test:styleable/AppBarLayoutStates = 0x7f10000b
com.github.barteksc.pdfviewer.test:styleable/AppBarLayout = 0x7f10000a
com.github.barteksc.pdfviewer.test:styleable/AnimatedStateListDrawableTransition = 0x7f100009
com.github.barteksc.pdfviewer.test:styleable/AlertDialog = 0x7f100006
com.github.barteksc.pdfviewer.test:styleable/ActionMenuView = 0x7f100003
com.github.barteksc.pdfviewer.test:styleable/ActionBar = 0x7f100000
com.github.barteksc.pdfviewer.test:style/Widget.Support.CoordinatorLayout = 0x7f0f045c
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f0f045a
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f0f0459
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f0f0458
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Toolbar = 0x7f0f0457
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f0f0455
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f0f0453
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f0f0451
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f0f044f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Button = 0x7f0f044e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f0f044a
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f0f0448
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f0f0446
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f0f0442
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f0f0441
com.github.barteksc.pdfviewer.test:styleable/GradientColorItem = 0x7f100039
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f0f0440
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f0f043e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f0f043c
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f0f043b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f0f0435
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f0f0431
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f0f0430
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f0f042e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f0f042b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f0f0426
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f0f0422
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f0f0420
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f0f041f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f0f041d
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f0f041c
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f0f041b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f0f0419
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f0f0416
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar = 0x7f0f0414
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f0f0413
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f0f0411
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f0f040d
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f0f040c
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f0f040b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f0f0409
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f0f0408
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f0f0406
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Chip.Filter = 0x7f0f0404
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Chip.Action = 0x7f0f0401
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f0f03fe
com.github.barteksc.pdfviewer.test:styleable/StateSet = 0x7f100080
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f0f03fd
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f0f03fc
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f0f03fb
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f0f03f9
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.TextButton = 0x7f0f03f7
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f0f03f6
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button = 0x7f0f03f3
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f0f03f2
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomSheet = 0x7f0f03f1
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f0f03f0
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f0f03ef
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f0f03e7
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f0f03e4
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f0f03f5
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f0f03e1
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f0f03df
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Tooltip = 0x7f0f03dd
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Toolbar.Surface = 0x7f0f03dc
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Toolbar.OnSurface = 0x7f0f03db
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f0f03d7
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f0f03d2
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f0f03cf
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f0f03ce
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f0f03c5
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SideSheet.Modal = 0x7f0f03c4
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SideSheet.Detached = 0x7f0f03c3
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SearchView = 0x7f0f03bf
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SearchBar.Outlined = 0x7f0f03be
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.NavigationRailView = 0x7f0f042d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SearchBar = 0x7f0f03bd
com.github.barteksc.pdfviewer.test:style/Widget.Material3.PopupMenu.Overflow = 0x7f0f03ba
com.github.barteksc.pdfviewer.test:style/Widget.Material3.PopupMenu = 0x7f0f03b7
com.github.barteksc.pdfviewer.test:style/Widget.Material3.NavigationRailView.Badge = 0x7f0f03b5
com.github.barteksc.pdfviewer.test:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f0f03b4
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f0f0452
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f0f03b1
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f0f03b0
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f0f03af
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f0f03ac
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialDivider.Heavy = 0x7f0f03a9
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialDivider = 0x7f0f03a8
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f0f03a6
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f0f03a3
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f0f03a2
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f0f039c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f0f039b
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f0f039a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f0f0398
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f0f0396
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f0f0395
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Day = 0x7f0f0392
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar = 0x7f0f0391
com.github.barteksc.pdfviewer.test:style/Widget.Material3.LinearProgressIndicator = 0x7f0f038f
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Light.ActionBar.Solid = 0x7f0f038e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f0f038d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f0f0389
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f0f0387
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f0f0385
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f0f0384
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f0f0382
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f0f0380
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f0f037e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f0f037d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f0f037b
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f0f0376
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CollapsingToolbar.Large = 0x7f0f0373
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f0f0371
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f0f036f
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ChipGroup = 0x7f0f036d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f0f036a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Input.Icon = 0x7f0f0369
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Input.Elevated = 0x7f0f0368
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Filter.Elevated = 0x7f0f0366
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Assist = 0x7f0f0363
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CardView.Filled = 0x7f0f0360
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f0f0358
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TextButton.Dialog = 0x7f0f0357
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TextButton = 0x7f0f0356
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f0f0352
com.github.barteksc.pdfviewer.test:styleable/SwitchMaterial = 0x7f100082
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.IconButton.Filled = 0x7f0f0351
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.Icon = 0x7f0f034f
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f0f034e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.ElevatedButton = 0x7f0f034d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button = 0x7f0f034c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomSheet.DragHandle = 0x7f0f034a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomSheet = 0x7f0f0349
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomNavigationView = 0x7f0f0347
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomAppBar.Legacy = 0x7f0f0346
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomAppBar = 0x7f0f0344
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Badge.AdjustToBounds = 0x7f0f0343
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Badge = 0x7f0f0342
com.github.barteksc.pdfviewer.test:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f0f0341
com.github.barteksc.pdfviewer.test:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f0f033f
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ActionBar.Solid = 0x7f0f033b
com.github.barteksc.pdfviewer.test:style/Widget.Design.TextInputEditText = 0x7f0f0339
com.github.barteksc.pdfviewer.test:style/Widget.Design.TabLayout = 0x7f0f0338
com.github.barteksc.pdfviewer.test:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f0f0336
com.github.barteksc.pdfviewer.test:style/Widget.Design.BottomSheet.Modal = 0x7f0f0332
com.github.barteksc.pdfviewer.test:style/Widget.Design.AppBarLayout = 0x7f0f0330
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f032d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f0327
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0325
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0323
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0320
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.RatingBar = 0x7f0f031f
com.github.barteksc.pdfviewer.test:styleable/AnimatedStateListDrawableItem = 0x7f100008
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ProgressBar = 0x7f0f031d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ListView.Menu = 0x7f0f0319
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ListView.DropDown = 0x7f0f0318
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0312
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f030e
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f030d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f030b
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f030a
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f0309
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0305
com.github.barteksc.pdfviewer.test:styleable/SideSheetBehavior_Layout = 0x7f100078
com.github.barteksc.pdfviewer.test:style/Widget.Design.BottomNavigationView = 0x7f0f0331
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0303
com.github.barteksc.pdfviewer.test:styleable/MotionTelltales = 0x7f100063
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0301
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f02f9
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f02f7
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActivityChooserView = 0x7f0f02ee
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionMode = 0x7f0f02ed
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionButton = 0x7f0f02ea
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f02e9
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f02e7
com.github.barteksc.pdfviewer.test:style/WhiteBackgroundTheme = 0x7f0f02e4
com.github.barteksc.pdfviewer.test:style/WhiteBackgroundDialogTheme = 0x7f0f02e3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f0f02e2
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f0f02de
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f0f02dd
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f0f02dc
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f0f02da
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f0f02d9
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f0f02d7
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f0f02d1
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f0f02cf
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f0f02ce
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f0f02cb
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f0f02c9
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f0f02c6
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f0f02c4
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f0f02c3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f0f02c2
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f0f02c1
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomAppBar = 0x7f0f03eb
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f0f02bd
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f0f02bc
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents = 0x7f0f02bb
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f0f02ba
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f0f02b8
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.TextInputEditText = 0x7f0f02b4
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.TabLayout = 0x7f0f02b3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Snackbar = 0x7f0f02b2
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Search = 0x7f0f02b0
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f0f02af
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.NavigationView = 0x7f0f02ae
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f0f0445
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.NavigationRailView = 0x7f0f02ad
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f0f02ac
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f0f02ab
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f0f02a5
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f0f02a3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f0f02a2
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f0f02a1
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f0f02a0
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f0f029e
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f0f029d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f0f0299
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f0f0296
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Dialog = 0x7f0f0294
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f0f0293
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f0f0291
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Dark = 0x7f0f0290
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f0f028d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f0f028c
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.TextButton = 0x7f0f028b
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f0f0287
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button = 0x7f0f0286
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f0f0281
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f0f0280
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f0f027f
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f0f027d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Design.TextInputEditText = 0x7f0f027a
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.Light = 0x7f0f0279
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f0275
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f0f02b1
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f0274
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.Dark = 0x7f0f0273
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat = 0x7f0f0271
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f0f026d
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f0f026b
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f0f026a
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f0f0268
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f0f0267
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f0f0266
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f0f0265
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f0f0263
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f0f0260
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light = 0x7f0f025f
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f0f025b
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f0f0258
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0306
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.Alert = 0x7f0f0257
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog = 0x7f0f0256
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f0f0254
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f0f0252
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f0f024c
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f0f024b
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f0f024a
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f0f0249
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f0f0247
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.CompactMenu = 0x7f0f0245
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f0f0243
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents = 0x7f0f0242
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.NoActionBar = 0x7f0f0240
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.DialogWhenLarge = 0x7f0f023f
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.Dialog = 0x7f0f023c
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.BottomSheetDialog = 0x7f0f023b
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DynamicColors.Light = 0x7f0f0239
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DynamicColors.Dark = 0x7f0f0237
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.NoActionBar = 0x7f0f0235
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f0f0234
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f0f0233
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.Dialog = 0x7f0f0231
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f0f0230
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.SideSheetDialog = 0x7f0f022e
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f0f022b
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f0f025d
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.Dialog = 0x7f0f0229
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark = 0x7f0f0227
com.github.barteksc.pdfviewer.test:style/Theme.Design.NoActionBar = 0x7f0f0226
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ChipGroup = 0x7f0f0405
com.github.barteksc.pdfviewer.test:style/Theme.Design.Light.NoActionBar = 0x7f0f0225
com.github.barteksc.pdfviewer.test:style/Theme.Design.Light.BottomSheetDialog = 0x7f0f0224
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f021d
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f021c
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f0f027e
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0217
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f0216
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f0213
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f020f
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f020e
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight = 0x7f0f020d
com.github.barteksc.pdfviewer.test:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f020a
com.github.barteksc.pdfviewer.test:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0208
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Tooltip = 0x7f0f0207
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f0f0204
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Headline6 = 0x7f0f0202
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f0f03de
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Headline5 = 0x7f0f0201
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Headline4 = 0x7f0f0200
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Headline2 = 0x7f0f01fe
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Headline1 = 0x7f0f01fd
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Button = 0x7f0f01fa
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.TitleSmall = 0x7f0f01f6
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.SearchView = 0x7f0f01f2
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.SearchBar = 0x7f0f01f1
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.LabelSmall = 0x7f0f01ef
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.LabelLarge = 0x7f0f01ed
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.HeadlineLarge = 0x7f0f01ea
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.DisplaySmall = 0x7f0f01e9
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.DisplayMedium = 0x7f0f01e8
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.DisplayLarge = 0x7f0f01e7
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.BodySmall = 0x7f0f01e6
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.ActionBar.Title = 0x7f0f01e3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f0f029c
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f0f01e1
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f0f01e0
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f0f01de
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f0f01dd
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f0f01dc
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f0f01db
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f0f02d3
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f0f01d9
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f0f01d8
com.github.barteksc.pdfviewer.test:styleable/CustomAttribute = 0x7f10002b
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f0f01d6
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f0f01d5
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f0f01d4
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f0f01d3
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.PopupMenu = 0x7f0f031a
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Tab = 0x7f0f01d2
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Suffix = 0x7f0f01d1
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Snackbar.Message = 0x7f0f01d0
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CheckedTextView = 0x7f0f0400
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Hint = 0x7f0f01cd
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.HelperText = 0x7f0f01cc
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Error = 0x7f0f01cb
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Counter = 0x7f0f01c9
com.github.barteksc.pdfviewer.test:styleable/ScrollBar = 0x7f100072
com.github.barteksc.pdfviewer.test:style/TextAppearance.Compat.Notification.Time = 0x7f0f01c6
com.github.barteksc.pdfviewer.test:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f01c5
com.github.barteksc.pdfviewer.test:style/TextAppearance.Compat.Notification.Info = 0x7f0f01c4
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f01c2
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f01c1
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f01bb
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f01ba
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f01b9
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f01b8
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f01b7
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Tooltip = 0x7f0f01af
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f01ae
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Title = 0x7f0f01ad
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Subhead = 0x7f0f01ab
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f01a7
com.github.barteksc.pdfviewer.test:styleable/MaterialToolbar = 0x7f10005a
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f01a3
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f01a1
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f01a0
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Large = 0x7f0f019e
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Inverse = 0x7f0f019d
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.BodyMedium = 0x7f0f01e5
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Headline = 0x7f0f019c
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Display2 = 0x7f0f0199
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f0f0192
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f0f0191
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f0f0190
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f0f018e
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f0f018d
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f0f018c
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f0f018a
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f0f0186
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f0f017f
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f0f017d
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.MaterialComponents.Badge = 0x7f0f017b
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Tooltip = 0x7f0f0179
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f0f0177
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.None = 0x7f0f0173
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f0f02d6
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.Medium = 0x7f0f0172
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.Large = 0x7f0f0171
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.Full = 0x7f0f0170
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f0f016f
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f0f016d
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f0f016c
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f0f016a
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f0f0167
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f0f0166
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f0f0163
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f0f0162
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f0f015f
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f0f015b
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f0f015a
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f0f0156
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f0152
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f0150
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f014d
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f014c
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f014b
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f0149
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f0147
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f0f0253
com.github.barteksc.pdfviewer.test:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f0144
com.github.barteksc.pdfviewer.test:style/Platform.Widget.AppCompat.Spinner = 0x7f0f0143
com.github.barteksc.pdfviewer.test:style/Platform.V25.AppCompat.Light = 0x7f0f0142
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f019f
com.github.barteksc.pdfviewer.test:style/Platform.V25.AppCompat = 0x7f0f0141
com.github.barteksc.pdfviewer.test:style/Platform.V21.AppCompat.Light = 0x7f0f0140
com.github.barteksc.pdfviewer.test:style/Platform.V21.AppCompat = 0x7f0f013f
com.github.barteksc.pdfviewer.test:style/Platform.ThemeOverlay.AppCompat = 0x7f0f013c
com.github.barteksc.pdfviewer.test:style/Platform.MaterialComponents.Light.Dialog = 0x7f0f013b
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.NoActionBar = 0x7f0f026f
com.github.barteksc.pdfviewer.test:style/Platform.MaterialComponents = 0x7f0f0138
com.github.barteksc.pdfviewer.test:style/Platform.AppCompat.Light = 0x7f0f0137
com.github.barteksc.pdfviewer.test:style/Platform.AppCompat = 0x7f0f0136
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f0f0135
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f0f0132
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f0f0130
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f0f02a8
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f0f012e
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f0f012d
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents = 0x7f0f012c
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f0f012b
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f0f0129
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f0f0128
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f0f0127
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f0f0126
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3 = 0x7f0f0122
com.github.barteksc.pdfviewer.test:style/CardView.Dark = 0x7f0f0120
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0311
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.TextView = 0x7f0f011e
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f0f011d
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.Snackbar = 0x7f0f011b
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f0f0118
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f0f0117
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f0f0116
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f0f0115
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f0f010f
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.TabLayout = 0x7f0f010e
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f0f010c
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f0f0107
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f0f0105
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.CollapsingToolbar = 0x7f0f0102
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.BottomNavigationView = 0x7f0f00ff
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.ActionBar.Solid = 0x7f0f00fd
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f00f7
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f00f5
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f00f3
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f00f1
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f00ee
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f00eb
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f00ea
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f00e9
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ListView = 0x7f0f00e7
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f00e6
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f00e4
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f00e3
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f00e1
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f00df
com.github.barteksc.pdfviewer.test:styleable/FragmentContainerView = 0x7f100037
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f00de
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ImageButton = 0x7f0f00dc
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.EditText = 0x7f0f00db
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f00da
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f00d3
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f00d1
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f00cf
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f00ce
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f00cb
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f00c8
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionButton = 0x7f0f00c7
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f00c6
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f00c5
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f00c4
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f00c3
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionBar = 0x7f0f00c2
com.github.barteksc.pdfviewer.test:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f00bf
com.github.barteksc.pdfviewer.test:style/Base.V7.Theme.AppCompat.Light = 0x7f0f00bc
com.github.barteksc.pdfviewer.test:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f00bb
com.github.barteksc.pdfviewer.test:style/Base.V7.Theme.AppCompat = 0x7f0f00ba
com.github.barteksc.pdfviewer.test:style/Base.V28.Theme.AppCompat = 0x7f0f00b8
com.github.barteksc.pdfviewer.test:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f00b7
com.github.barteksc.pdfviewer.test:style/Base.V26.Theme.AppCompat.Light = 0x7f0f00b6
com.github.barteksc.pdfviewer.test:style/Base.V26.Theme.AppCompat = 0x7f0f00b5
com.github.barteksc.pdfviewer.test:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f0f00b4
com.github.barteksc.pdfviewer.test:style/Base.V24.Theme.Material3.Light = 0x7f0f00b3
com.github.barteksc.pdfviewer.test:style/Base.V23.Theme.AppCompat.Light = 0x7f0f00b0
com.github.barteksc.pdfviewer.test:style/Base.V23.Theme.AppCompat = 0x7f0f00af
com.github.barteksc.pdfviewer.test:style/Base.V22.Theme.AppCompat.Light = 0x7f0f00ae
com.github.barteksc.pdfviewer.test:style/Base.V22.Theme.AppCompat = 0x7f0f00ad
com.github.barteksc.pdfviewer.test:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f00a9
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f0f00a8
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f00a4
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.AppCompat = 0x7f0f00a1
com.github.barteksc.pdfviewer.test:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f0f009f
com.github.barteksc.pdfviewer.test:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f0f009e
com.github.barteksc.pdfviewer.test:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f0f009d
com.github.barteksc.pdfviewer.test:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f0f009c
com.github.barteksc.pdfviewer.test:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f0f009b
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f0f0098
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f0f0096
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Headline3 = 0x7f0f01ff
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f0f0094
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f0f0093
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents = 0x7f0f0091
com.github.barteksc.pdfviewer.test:style/Widget.Material3.DrawerLayout = 0x7f0f0379
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f0f0090
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Light = 0x7f0f008d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.SearchView = 0x7f0f0322
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f0f0088
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.CardView = 0x7f0f0100
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f0f0087
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f0f0085
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f0f0255
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f0f0084
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f0f007f
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f007b
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat = 0x7f0f0078
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f0f0077
com.github.barteksc.pdfviewer.test:styleable/KeyTrigger = 0x7f100043
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f0f0071
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light = 0x7f0f006e
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f0f006d
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f0f006a
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f0f0069
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f0f0063
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Light.Dialog = 0x7f0f0061
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Light = 0x7f0f005f
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f0f005d
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f0f005c
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f0f005a
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0057
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0055
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0054
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f007e
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f0051
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f0050
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f004f
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f004e
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Dialog = 0x7f0f004d
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f004a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Item = 0x7f0f03a1
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f0049
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0048
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f0f0046
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.MaterialComponents.Button = 0x7f0f0045
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.Material3.Search = 0x7f0f0043
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0041
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0040
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f003d
com.github.barteksc.pdfviewer.test:style/TextAppearance.Compat.Notification.Title = 0x7f0f01c7
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f003c
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f003b
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.AppCompat.Light = 0x7f0f00a3
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f0039
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f0f0269
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f0038
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f0037
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f0035
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f0034
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0033
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f0f0206
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f0030
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Title = 0x7f0f002f
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f002e
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f0027
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0022
com.github.barteksc.pdfviewer.test:styleable/MaterialTimePicker = 0x7f100059
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0021
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f001c
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f002a
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f001a
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat = 0x7f0f0016
com.github.barteksc.pdfviewer.test:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f0f0014
com.github.barteksc.pdfviewer.test:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f0012
com.github.barteksc.pdfviewer.test:style/Base.DialogWindowTitle.AppCompat = 0x7f0f0011
com.github.barteksc.pdfviewer.test:style/Base.CardView = 0x7f0f0010
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f0f0099
com.github.barteksc.pdfviewer.test:style/Base.Animation.AppCompat.Tooltip = 0x7f0f000f
com.github.barteksc.pdfviewer.test:style/Base.AlertDialog.AppCompat.Light = 0x7f0f000c
com.github.barteksc.pdfviewer.test:style/Base.AlertDialog.AppCompat = 0x7f0f000b
com.github.barteksc.pdfviewer.test:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f0f000a
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f0f0082
com.github.barteksc.pdfviewer.test:style/Animation.Material3.BottomSheetDialog = 0x7f0f0006
com.github.barteksc.pdfviewer.test:style/Animation.AppCompat.Tooltip = 0x7f0f0004
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f0f0250
com.github.barteksc.pdfviewer.test:style/AlertDialog.AppCompat = 0x7f0f0000
com.github.barteksc.pdfviewer.test:string/searchbar_scrolling_view_behavior = 0x7f0e00a0
com.github.barteksc.pdfviewer.test:string/search_menu_title = 0x7f0e009f
com.github.barteksc.pdfviewer.test:string/path_password_eye_mask_visible = 0x7f0e009d
com.github.barteksc.pdfviewer.test:string/path_password_eye_mask_strike_through = 0x7f0e009c
com.github.barteksc.pdfviewer.test:string/path_password_eye = 0x7f0e009b
com.github.barteksc.pdfviewer.test:string/password_toggle_content_description = 0x7f0e009a
com.github.barteksc.pdfviewer.test:string/mtrl_timepicker_cancel = 0x7f0e0098
com.github.barteksc.pdfviewer.test:string/mtrl_switch_track_decoration_path = 0x7f0e0096
com.github.barteksc.pdfviewer.test:string/mtrl_switch_thumb_path_pressed = 0x7f0e0094
com.github.barteksc.pdfviewer.test:string/mtrl_switch_thumb_path_morphing = 0x7f0e0092
com.github.barteksc.pdfviewer.test:string/mtrl_switch_thumb_path_checked = 0x7f0e0091
com.github.barteksc.pdfviewer.test:string/mtrl_picker_toggle_to_text_input_mode = 0x7f0e008e
com.github.barteksc.pdfviewer.test:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f0e008c
com.github.barteksc.pdfviewer.test:string/mtrl_picker_text_input_year_abbr = 0x7f0e008a
com.github.barteksc.pdfviewer.test:string/mtrl_picker_text_input_month_abbr = 0x7f0e0089
com.github.barteksc.pdfviewer.test:string/mtrl_picker_text_input_day_abbr = 0x7f0e0088
com.github.barteksc.pdfviewer.test:string/mtrl_picker_text_input_date_range_end_hint = 0x7f0e0086
com.github.barteksc.pdfviewer.test:string/mtrl_picker_text_input_date_hint = 0x7f0e0085
com.github.barteksc.pdfviewer.test:string/mtrl_picker_range_header_unselected = 0x7f0e0082
com.github.barteksc.pdfviewer.test:string/mtrl_picker_range_header_selected = 0x7f0e0080
com.github.barteksc.pdfviewer.test:string/mtrl_picker_range_header_only_start_selected = 0x7f0e007f
com.github.barteksc.pdfviewer.test:string/mtrl_picker_range_header_only_end_selected = 0x7f0e007e
com.github.barteksc.pdfviewer.test:string/mtrl_picker_out_of_range = 0x7f0e007d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.PopupWindow = 0x7f0f031c
com.github.barteksc.pdfviewer.test:string/mtrl_picker_navigate_to_year_description = 0x7f0e007c
com.github.barteksc.pdfviewer.test:string/mtrl_picker_invalid_range = 0x7f0e007a
com.github.barteksc.pdfviewer.test:string/mtrl_picker_invalid_format_use = 0x7f0e0079
com.github.barteksc.pdfviewer.test:styleable/CoordinatorLayout = 0x7f100029
com.github.barteksc.pdfviewer.test:string/mtrl_picker_invalid_format = 0x7f0e0077
com.github.barteksc.pdfviewer.test:string/mtrl_picker_date_header_unselected = 0x7f0e0074
com.github.barteksc.pdfviewer.test:string/mtrl_picker_date_header_selected = 0x7f0e0072
com.github.barteksc.pdfviewer.test:string/mtrl_exceed_max_badge_number_content_description = 0x7f0e0069
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f021f
com.github.barteksc.pdfviewer.test:string/mtrl_chip_close_icon_content_description = 0x7f0e0068
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_state_description_unchecked = 0x7f0e0067
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_state_description_indeterminate = 0x7f0e0066
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_path_unchecked = 0x7f0e0064
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_path_name = 0x7f0e0063
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_path_group_name = 0x7f0e0062
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_path_checked = 0x7f0e0061
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f0e005f
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_icon_path_group_name = 0x7f0e005e
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_icon_path_checked = 0x7f0e005d
com.github.barteksc.pdfviewer.test:string/mtrl_badge_numberless_content_description = 0x7f0e005c
com.github.barteksc.pdfviewer.test:string/material_timepicker_pm = 0x7f0e0059
com.github.barteksc.pdfviewer.test:string/material_timepicker_minute = 0x7f0e0058
com.github.barteksc.pdfviewer.test:string/material_timepicker_hour = 0x7f0e0057
com.github.barteksc.pdfviewer.test:string/material_slider_value = 0x7f0e0054
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f0f0444
com.github.barteksc.pdfviewer.test:string/material_slider_range_end = 0x7f0e0052
com.github.barteksc.pdfviewer.test:string/material_motion_easing_standard = 0x7f0e0051
com.github.barteksc.pdfviewer.test:string/material_motion_easing_linear = 0x7f0e0050
com.github.barteksc.pdfviewer.test:string/material_motion_easing_emphasized = 0x7f0e004f
com.github.barteksc.pdfviewer.test:string/material_motion_easing_decelerated = 0x7f0e004e
com.github.barteksc.pdfviewer.test:string/material_minute_selection = 0x7f0e004b
com.github.barteksc.pdfviewer.test:string/material_clock_toggle_content_description = 0x7f0e0047
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_linear = 0x7f0e0042
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f00f0
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_legacy_accelerate = 0x7f0e0040
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f0f03d0
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Snackbar.FullWidth = 0x7f0f03c9
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f003f
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_legacy = 0x7f0e003f
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_emphasized_path_data = 0x7f0e003e
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f0e003d
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.PopupMenu = 0x7f0f0433
com.github.barteksc.pdfviewer.test:string/m3_ref_typeface_brand_medium = 0x7f0e0037
com.github.barteksc.pdfviewer.test:string/m3_exceed_max_badge_text_suffix = 0x7f0e0036
com.github.barteksc.pdfviewer.test:string/item_view_role_description = 0x7f0e0035
com.github.barteksc.pdfviewer.test:string/icon_content_description = 0x7f0e0034
com.github.barteksc.pdfviewer.test:string/hide_bottom_view_on_scroll_behavior = 0x7f0e0033
com.github.barteksc.pdfviewer.test:string/exposed_dropdown_menu_content_description = 0x7f0e0030
com.github.barteksc.pdfviewer.test:string/clear_text_end_icon_content_description = 0x7f0e002d
com.github.barteksc.pdfviewer.test:string/character_counter_pattern = 0x7f0e002c
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0304
com.github.barteksc.pdfviewer.test:string/character_counter_overflowed_content_description = 0x7f0e002b
com.github.barteksc.pdfviewer.test:string/call_notification_screening_text = 0x7f0e0029
com.github.barteksc.pdfviewer.test:string/call_notification_ongoing_text = 0x7f0e0028
com.github.barteksc.pdfviewer.test:string/call_notification_hang_up_action = 0x7f0e0026
com.github.barteksc.pdfviewer.test:string/bottomsheet_drag_handle_content_description = 0x7f0e0022
com.github.barteksc.pdfviewer.test:string/bottomsheet_action_expand = 0x7f0e001f
com.github.barteksc.pdfviewer.test:string/bottomsheet_action_collapse = 0x7f0e001e
com.github.barteksc.pdfviewer.test:string/appbar_scrolling_view_behavior = 0x7f0e001c
com.github.barteksc.pdfviewer.test:string/androidx_startup = 0x7f0e001b
com.github.barteksc.pdfviewer.test:string/abc_toolbar_collapse_description = 0x7f0e001a
com.github.barteksc.pdfviewer.test:string/abc_shareactionprovider_share_with_application = 0x7f0e0019
com.github.barteksc.pdfviewer.test:string/abc_searchview_description_submit = 0x7f0e0016
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TabLayout = 0x7f0f043d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f0f02cc
com.github.barteksc.pdfviewer.test:string/abc_prepend_shortcut_label = 0x7f0e0011
com.github.barteksc.pdfviewer.test:string/abc_menu_sym_shortcut_label = 0x7f0e0010
com.github.barteksc.pdfviewer.test:string/abc_menu_space_shortcut_label = 0x7f0e000f
com.github.barteksc.pdfviewer.test:string/abc_menu_shift_shortcut_label = 0x7f0e000e
com.github.barteksc.pdfviewer.test:string/abc_menu_meta_shortcut_label = 0x7f0e000d
com.github.barteksc.pdfviewer.test:string/abc_menu_enter_shortcut_label = 0x7f0e000b
com.github.barteksc.pdfviewer.test:string/abc_menu_ctrl_shortcut_label = 0x7f0e0009
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.NoActionBar = 0x7f0f022d
com.github.barteksc.pdfviewer.test:string/abc_capital_off = 0x7f0e0006
com.github.barteksc.pdfviewer.test:string/abc_activitychooserview_choose_application = 0x7f0e0005
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Snackbar = 0x7f0f03c8
com.github.barteksc.pdfviewer.test:string/abc_activity_chooser_view_see_all = 0x7f0e0004
com.github.barteksc.pdfviewer.test:string/abc_action_mode_done = 0x7f0e0003
com.github.barteksc.pdfviewer.test:string/abc_action_menu_overflow_description = 0x7f0e0002
com.github.barteksc.pdfviewer.test:string/abc_action_bar_home_description = 0x7f0e0000
com.github.barteksc.pdfviewer.test:plurals/mtrl_badge_content_description = 0x7f0d0000
com.github.barteksc.pdfviewer.test:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f0f012f
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0173
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c0170
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016d
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016c
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c016a
com.github.barteksc.pdfviewer.test:style/Base.V24.Theme.Material3.Dark = 0x7f0f00b1
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0169
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0167
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0166
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0165
com.github.barteksc.pdfviewer.test:style/Widget.Compat.NotificationActionContainer = 0x7f0f032e
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0163
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015f
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015e
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015d
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f0f0062
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c015a
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0159
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0157
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0156
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0153
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f00d7
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_headline_type = 0x7f0c0152
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_headline_color = 0x7f0c0151
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_container_shape = 0x7f0c0150
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.AppBarLayout = 0x7f0f033d
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014c
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c014a
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f02eb
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0149
com.github.barteksc.pdfviewer.test:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0147
com.github.barteksc.pdfviewer.test:macro/m3_comp_text_button_label_text_type = 0x7f0c0146
com.github.barteksc.pdfviewer.test:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0144
com.github.barteksc.pdfviewer.test:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0143
com.github.barteksc.pdfviewer.test:string/mtrl_picker_toggle_to_day_selection = 0x7f0e008d
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c0140
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013e
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CircularProgressIndicator = 0x7f0f036e
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013b
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c013a
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f003a
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0135
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0134
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016b
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0133
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0131
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c0130
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f0f0428
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_track_color = 0x7f0c012f
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.ActionBar = 0x7f0f027c
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012c
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0128
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0127
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_handle_color = 0x7f0c0125
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0124
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f001f
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0123
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0122
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c0120
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011f
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011e
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011d
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011c
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c011a
com.github.barteksc.pdfviewer.test:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0118
com.github.barteksc.pdfviewer.test:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0116
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Button.Colored = 0x7f0f02f4
com.github.barteksc.pdfviewer.test:macro/m3_comp_snackbar_container_shape = 0x7f0c0115
com.github.barteksc.pdfviewer.test:macro/m3_comp_snackbar_container_color = 0x7f0c0114
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_label_label_text_color = 0x7f0c0113
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_handle_color = 0x7f0c0110
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010e
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010d
com.github.barteksc.pdfviewer.test:macro/m3_comp_sheet_side_docked_container_color = 0x7f0c010a
com.github.barteksc.pdfviewer.test:string/material_hour_selection = 0x7f0e0049
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0103
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c0101
com.github.barteksc.pdfviewer.test:styleable/MaterialTextAppearance = 0x7f100057
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00ff
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f0f02aa
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fe
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fc
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00fb
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0278
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f9
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f0f024f
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f8
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f6
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f0029
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_divider_color = 0x7f0c00f4
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f1
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00f0
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ef
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015c
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ee
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ed
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00ec
com.github.barteksc.pdfviewer.test:styleable/ActionMenuItemView = 0x7f100002
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.Button = 0x7f0f0180
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_input_text_color = 0x7f0c00ea
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e8
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_container_surface_tint_layer_color = 0x7f0c00e7
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f0f0109
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f002d
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
com.github.barteksc.pdfviewer.test:string/abc_action_bar_up_description = 0x7f0e0001
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
com.github.barteksc.pdfviewer.test:string/material_timepicker_clock_mode_description = 0x7f0e0056
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat = 0x7f0f0193
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d5
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d4
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f0f040f
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d3
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SideSheet = 0x7f0f03c2
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Spinner = 0x7f0f00f6
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00d1
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00ce
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cd
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00cb
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f0f029a
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00ca
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c7
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c5
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c3
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00c1
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00c0
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00bb
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b9
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b7
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b4
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b2
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ad
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_container_shape = 0x7f0c00ab
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_container_color = 0x7f0c00aa
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a9
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a8
com.github.barteksc.pdfviewer.test:styleable/NavigationRailView = 0x7f100066
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f0f0394
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a7
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a6
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a3
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c00a0
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009e
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f0f0297
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009c
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_container_color = 0x7f0c009b
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0099
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0098
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0097
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0096
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0093
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ShapeableImageView = 0x7f0f0438
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0321
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c0092
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f0f006b
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c0090
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008f
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008d
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c008a
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0088
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0087
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_container_color = 0x7f0c0086
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f0f03d1
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0085
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0084
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c0081
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007f
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007e
com.github.barteksc.pdfviewer.test:styleable/Insets = 0x7f10003b
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c007a
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0079
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0078
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0077
com.github.barteksc.pdfviewer.test:styleable/CollapsingToolbarLayout = 0x7f100021
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0075
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0073
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c0070
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006e
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f001b
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0132
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c006b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f0f0415
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Button = 0x7f0f02f0
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c006a
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0069
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0067
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0066
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0065
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f02e6
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0062
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c0061
com.github.barteksc.pdfviewer.test:macro/m3_comp_menu_container_color = 0x7f0c0060
com.github.barteksc.pdfviewer.test:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0119
com.github.barteksc.pdfviewer.test:macro/m3_comp_linear_progress_indicator_active_indicator_color = 0x7f0c005e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Assist.Elevated = 0x7f0f0364
com.github.barteksc.pdfviewer.test:macro/m3_comp_input_chip_label_text_type = 0x7f0c005d
com.github.barteksc.pdfviewer.test:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005b
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f0f02d8
com.github.barteksc.pdfviewer.test:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c005a
com.github.barteksc.pdfviewer.test:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0059
com.github.barteksc.pdfviewer.test:macro/m3_comp_filter_chip_container_shape = 0x7f0c0058
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0057
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0056
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0055
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0052
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0051
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f0328
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c0050
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004f
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c004a
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_card_container_shape = 0x7f0c0048
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_card_container_color = 0x7f0c0047
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f0f043f
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_button_label_text_type = 0x7f0c0046
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_button_container_color = 0x7f0c0044
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0043
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Chip = 0x7f0f028e
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_autocomplete_menu_list_item_selected_container_color = 0x7f0c0042
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CardView.Elevated = 0x7f0f035f
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Body1 = 0x7f0f0194
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_tertiary_container_color = 0x7f0c0040
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_surface_icon_color = 0x7f0c003f
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003d
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_secondary_container_color = 0x7f0c003c
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003b
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c003a
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_primary_icon_color = 0x7f0c0039
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f0079
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_primary_container_shape = 0x7f0c0038
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_primary_container_color = 0x7f0c0037
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0036
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0035
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0032
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0031
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f0f024d
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c0030
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f00e5
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002f
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002d
com.github.barteksc.pdfviewer.test:string/abc_capital_on = 0x7f0e0007
com.github.barteksc.pdfviewer.test:macro/m3_comp_elevated_card_container_color = 0x7f0c002b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f0f0425
com.github.barteksc.pdfviewer.test:macro/m3_comp_elevated_button_container_color = 0x7f0c002a
com.github.barteksc.pdfviewer.test:macro/m3_comp_divider_color = 0x7f0c0029
com.github.barteksc.pdfviewer.test:macro/m3_comp_dialog_container_shape = 0x7f0c0024
com.github.barteksc.pdfviewer.test:string/material_timepicker_text_input_mode_description = 0x7f0e005b
com.github.barteksc.pdfviewer.test:macro/m3_comp_dialog_container_color = 0x7f0c0023
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f0f035b
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0022
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c0020
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001f
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001e
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001d
com.github.barteksc.pdfviewer.test:string/abc_searchview_description_voice = 0x7f0e0017
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001b
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c001a
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0019
com.github.barteksc.pdfviewer.test:styleable/FontFamilyFont = 0x7f100034
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0018
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006f
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0017
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0013
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0011
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c0010
com.github.barteksc.pdfviewer.test:macro/m3_comp_circular_progress_indicator_active_indicator_color = 0x7f0c000d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TabLayout.Secondary = 0x7f0f03cd
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0104
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
com.github.barteksc.pdfviewer.test:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f00c1
com.github.barteksc.pdfviewer.test:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CompoundButton.RadioButton = 0x7f0f0377
com.github.barteksc.pdfviewer.test:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
com.github.barteksc.pdfviewer.test:macro/m3_comp_badge_color = 0x7f0c0002
com.github.barteksc.pdfviewer.test:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
com.github.barteksc.pdfviewer.test:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
com.github.barteksc.pdfviewer.test:layout/select_dialog_item_material = 0x7f0b0065
com.github.barteksc.pdfviewer.test:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f0209
com.github.barteksc.pdfviewer.test:layout/notification_template_part_time = 0x7f0b0064
com.github.barteksc.pdfviewer.test:layout/notification_template_icon_group = 0x7f0b0062
com.github.barteksc.pdfviewer.test:layout/notification_action_tombstone = 0x7f0b0060
com.github.barteksc.pdfviewer.test:layout/notification_action = 0x7f0b005f
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ae
com.github.barteksc.pdfviewer.test:layout/mtrl_search_view = 0x7f0b005e
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_header_toggle = 0x7f0b005a
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_header_selection_text = 0x7f0b0058
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_header_fullscreen = 0x7f0b0057
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_fullscreen = 0x7f0b0055
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_actions = 0x7f0b0053
com.github.barteksc.pdfviewer.test:layout/mtrl_navigation_rail_item = 0x7f0b0052
com.github.barteksc.pdfviewer.test:layout/mtrl_layout_snackbar_include = 0x7f0b0051
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_year = 0x7f0b004f
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f0329
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_months = 0x7f0b004d
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_month = 0x7f0b004a
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_horizontal = 0x7f0b0049
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_day_of_week = 0x7f0b0047
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_day = 0x7f0b0046
com.github.barteksc.pdfviewer.test:layout/mtrl_alert_select_dialog_item = 0x7f0b0042
com.github.barteksc.pdfviewer.test:layout/material_timepicker = 0x7f0b003c
com.github.barteksc.pdfviewer.test:layout/material_time_chip = 0x7f0b003a
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0023
com.github.barteksc.pdfviewer.test:layout/material_clockface_view = 0x7f0b0037
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f0f0348
com.github.barteksc.pdfviewer.test:layout/material_clock_period_toggle_land = 0x7f0b0035
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f0f0397
com.github.barteksc.pdfviewer.test:layout/material_chip_input_combo = 0x7f0b0031
com.github.barteksc.pdfviewer.test:layout/m3_side_sheet_dialog = 0x7f0b0030
com.github.barteksc.pdfviewer.test:layout/m3_auto_complete_simple_item = 0x7f0b002f
com.github.barteksc.pdfviewer.test:layout/m3_alert_dialog_title = 0x7f0b002e
com.github.barteksc.pdfviewer.test:layout/design_text_input_start_icon = 0x7f0b002b
com.github.barteksc.pdfviewer.test:layout/design_navigation_menu_item = 0x7f0b0029
com.github.barteksc.pdfviewer.test:layout/design_navigation_item_separator = 0x7f0b0026
com.github.barteksc.pdfviewer.test:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f0f009a
com.github.barteksc.pdfviewer.test:layout/design_bottom_navigation_item = 0x7f0b001d
com.github.barteksc.pdfviewer.test:layout/custom_dialog = 0x7f0b001c
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f0f028a
com.github.barteksc.pdfviewer.test:layout/abc_tooltip = 0x7f0b001b
com.github.barteksc.pdfviewer.test:layout/abc_select_dialog_material = 0x7f0b001a
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012e
com.github.barteksc.pdfviewer.test:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.github.barteksc.pdfviewer.test:layout/abc_screen_toolbar = 0x7f0b0017
com.github.barteksc.pdfviewer.test:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.github.barteksc.pdfviewer.test:layout/abc_screen_simple = 0x7f0b0015
com.github.barteksc.pdfviewer.test:layout/abc_screen_content_include = 0x7f0b0014
com.github.barteksc.pdfviewer.test:layout/abc_list_menu_item_radio = 0x7f0b0011
com.github.barteksc.pdfviewer.test:layout/abc_list_menu_item_layout = 0x7f0b0010
com.github.barteksc.pdfviewer.test:layout/abc_list_menu_item_icon = 0x7f0b000f
com.github.barteksc.pdfviewer.test:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.github.barteksc.pdfviewer.test:layout/abc_expanded_menu_layout = 0x7f0b000d
com.github.barteksc.pdfviewer.test:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.github.barteksc.pdfviewer.test:layout/abc_alert_dialog_material = 0x7f0b0009
com.github.barteksc.pdfviewer.test:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.github.barteksc.pdfviewer.test:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.github.barteksc.pdfviewer.test:layout/abc_action_mode_bar = 0x7f0b0004
com.github.barteksc.pdfviewer.test:layout/abc_action_menu_layout = 0x7f0b0003
com.github.barteksc.pdfviewer.test:layout/abc_action_bar_up_container = 0x7f0b0001
com.github.barteksc.pdfviewer.test:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
com.github.barteksc.pdfviewer.test:interpolator/mtrl_linear = 0x7f0a0010
com.github.barteksc.pdfviewer.test:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
com.github.barteksc.pdfviewer.test:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
com.github.barteksc.pdfviewer.test:styleable/Toolbar = 0x7f100089
com.github.barteksc.pdfviewer.test:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.github.barteksc.pdfviewer.test:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.github.barteksc.pdfviewer.test:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0136
com.github.barteksc.pdfviewer.test:integer/status_bar_notification_info_maxnum = 0x7f090043
com.github.barteksc.pdfviewer.test:integer/show_password_duration = 0x7f090042
com.github.barteksc.pdfviewer.test:integer/mtrl_view_visible = 0x7f090041
com.github.barteksc.pdfviewer.test:layout/design_layout_tab_icon = 0x7f0b0021
com.github.barteksc.pdfviewer.test:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f09003e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f0f0355
com.github.barteksc.pdfviewer.test:style/Widget.Design.CollapsingToolbar = 0x7f0f0333
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_track_viewport_height = 0x7f09003c
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_thumb_viewport_size = 0x7f09003b
com.github.barteksc.pdfviewer.test:string/call_notification_answer_video_action = 0x7f0e0024
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_inactive_track_color = 0x7f0c0111
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003a
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_thumb_pressed_duration = 0x7f090039
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f090038
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090037
com.github.barteksc.pdfviewer.test:integer/mtrl_card_anim_delay_ms = 0x7f090033
com.github.barteksc.pdfviewer.test:style/Animation.AppCompat.Dialog = 0x7f0f0002
com.github.barteksc.pdfviewer.test:integer/mtrl_calendar_year_selector_span = 0x7f090032
com.github.barteksc.pdfviewer.test:integer/mtrl_btn_anim_delay_ms = 0x7f09002e
com.github.barteksc.pdfviewer.test:integer/material_motion_path = 0x7f09002c
com.github.barteksc.pdfviewer.test:integer/material_motion_duration_short_1 = 0x7f09002a
com.github.barteksc.pdfviewer.test:integer/material_motion_duration_long_2 = 0x7f090027
com.github.barteksc.pdfviewer.test:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090024
com.github.barteksc.pdfviewer.test:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090021
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_short2 = 0x7f09001c
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007d
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_short1 = 0x7f09001b
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_medium4 = 0x7f09001a
com.github.barteksc.pdfviewer.test:layout/abc_dialog_title_material = 0x7f0b000c
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_medium3 = 0x7f090019
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_long4 = 0x7f090016
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f0f0228
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_long3 = 0x7f090015
com.github.barteksc.pdfviewer.test:string/mtrl_exceed_max_badge_number_suffix = 0x7f0e006a
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_long1 = 0x7f090013
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_extra_long2 = 0x7f090010
com.github.barteksc.pdfviewer.test:integer/m3_chip_anim_duration = 0x7f09000e
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f0f02be
com.github.barteksc.pdfviewer.test:integer/m3_card_anim_duration_ms = 0x7f09000d
com.github.barteksc.pdfviewer.test:integer/m3_card_anim_delay_ms = 0x7f09000c
com.github.barteksc.pdfviewer.test:integer/m3_btn_anim_duration_ms = 0x7f09000b
com.github.barteksc.pdfviewer.test:styleable/AppCompatTextHelper = 0x7f100010
com.github.barteksc.pdfviewer.test:integer/m3_btn_anim_delay_ms = 0x7f09000a
com.github.barteksc.pdfviewer.test:integer/hide_password_duration = 0x7f090008
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Bridge = 0x7f0f0066
com.github.barteksc.pdfviewer.test:integer/config_tooltipAnimTime = 0x7f090005
com.github.barteksc.pdfviewer.test:integer/bottom_sheet_slide_duration = 0x7f090003
com.github.barteksc.pdfviewer.test:integer/app_bar_elevation_anim_duration = 0x7f090002
com.github.barteksc.pdfviewer.test:integer/abc_config_activityShortDur = 0x7f090001
com.github.barteksc.pdfviewer.test:integer/abc_config_activityDefaultDur = 0x7f090000
com.github.barteksc.pdfviewer.test:id/wrap_content = 0x7f0801be
com.github.barteksc.pdfviewer.test:id/withText = 0x7f0801ba
com.github.barteksc.pdfviewer.test:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0801b5
com.github.barteksc.pdfviewer.test:id/view_offset_helper = 0x7f0801b3
com.github.barteksc.pdfviewer.test:id/unchecked = 0x7f0801ae
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f0f0262
com.github.barteksc.pdfviewer.test:id/transition_transform = 0x7f0801ac
com.github.barteksc.pdfviewer.test:id/transition_current_scene = 0x7f0801a8
com.github.barteksc.pdfviewer.test:id/transitionToStart = 0x7f0801a7
com.github.barteksc.pdfviewer.test:id/topPanel = 0x7f0801a4
com.github.barteksc.pdfviewer.test:id/top = 0x7f0801a3
com.github.barteksc.pdfviewer.test:id/toggle = 0x7f0801a2
com.github.barteksc.pdfviewer.test:id/title_template = 0x7f0801a1
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.RatingBar = 0x7f0f00ef
com.github.barteksc.pdfviewer.test:id/title = 0x7f08019f
com.github.barteksc.pdfviewer.test:id/textinput_placeholder = 0x7f08019b
com.github.barteksc.pdfviewer.test:macro/m3_comp_text_button_label_text_color = 0x7f0c0145
com.github.barteksc.pdfviewer.test:id/textinput_helper_text = 0x7f08019a
com.github.barteksc.pdfviewer.test:id/textinput_error = 0x7f080199
com.github.barteksc.pdfviewer.test:id/textTop = 0x7f080194
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f00dd
com.github.barteksc.pdfviewer.test:id/textSpacerNoTitle = 0x7f080192
com.github.barteksc.pdfviewer.test:id/textSpacerNoButtons = 0x7f080191
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0049
com.github.barteksc.pdfviewer.test:id/textEnd = 0x7f080190
com.github.barteksc.pdfviewer.test:id/text2 = 0x7f08018f
com.github.barteksc.pdfviewer.test:id/text = 0x7f08018e
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f0f02db
com.github.barteksc.pdfviewer.test:id/tag_unhandled_key_listeners = 0x7f08018c
com.github.barteksc.pdfviewer.test:id/tag_unhandled_key_event_manager = 0x7f08018b
com.github.barteksc.pdfviewer.test:id/tag_transition_group = 0x7f08018a
com.github.barteksc.pdfviewer.test:id/tag_on_receive_content_mime_types = 0x7f080187
com.github.barteksc.pdfviewer.test:id/tag_on_receive_content_listener = 0x7f080186
com.github.barteksc.pdfviewer.test:id/tag_accessibility_clickable_spans = 0x7f080182
com.github.barteksc.pdfviewer.test:id/submit_area = 0x7f08017f
com.github.barteksc.pdfviewer.test:styleable/DrawerArrowToggle = 0x7f10002c
com.github.barteksc.pdfviewer.test:id/submenuarrow = 0x7f08017e
com.github.barteksc.pdfviewer.test:id/staticLayout = 0x7f08017a
com.github.barteksc.pdfviewer.test:id/startVertical = 0x7f080179
com.github.barteksc.pdfviewer.test:id/startToEnd = 0x7f080178
com.github.barteksc.pdfviewer.test:id/startHorizontal = 0x7f080177
com.github.barteksc.pdfviewer.test:id/src_in = 0x7f080173
com.github.barteksc.pdfviewer.test:id/tag_state_description = 0x7f080189
com.github.barteksc.pdfviewer.test:id/src_atop = 0x7f080172
com.github.barteksc.pdfviewer.test:id/square = 0x7f080171
com.github.barteksc.pdfviewer.test:id/spline = 0x7f08016d
com.github.barteksc.pdfviewer.test:id/special_effects_controller_view_tag = 0x7f08016c
com.github.barteksc.pdfviewer.test:id/snap = 0x7f080169
com.github.barteksc.pdfviewer.test:id/snackbar_text = 0x7f080168
com.github.barteksc.pdfviewer.test:id/skipCollapsed = 0x7f080165
com.github.barteksc.pdfviewer.test:id/showTitle = 0x7f080163
com.github.barteksc.pdfviewer.test:id/showHome = 0x7f080162
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f0f0418
com.github.barteksc.pdfviewer.test:id/showCustom = 0x7f080161
com.github.barteksc.pdfviewer.test:id/shortcut = 0x7f080160
com.github.barteksc.pdfviewer.test:id/select_dialog_listview = 0x7f08015d
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_medium2 = 0x7f090018
com.github.barteksc.pdfviewer.test:id/search_voice_btn = 0x7f08015c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Primary = 0x7f0f0386
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.TitleLarge = 0x7f0f01f4
com.github.barteksc.pdfviewer.test:id/search_src_text = 0x7f08015b
com.github.barteksc.pdfviewer.test:id/search_plate = 0x7f08015a
com.github.barteksc.pdfviewer.test:id/search_mag_icon = 0x7f080159
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00ba
com.github.barteksc.pdfviewer.test:id/search_go_btn = 0x7f080158
com.github.barteksc.pdfviewer.test:id/search_close_btn = 0x7f080156
com.github.barteksc.pdfviewer.test:id/search_button = 0x7f080155
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f0f0157
com.github.barteksc.pdfviewer.test:id/search_badge = 0x7f080153
com.github.barteksc.pdfviewer.test:id/scrollView = 0x7f080151
com.github.barteksc.pdfviewer.test:layout/material_clock_display = 0x7f0b0032
com.github.barteksc.pdfviewer.test:id/scrollIndicatorUp = 0x7f080150
com.github.barteksc.pdfviewer.test:id/scrollIndicatorDown = 0x7f08014f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker = 0x7f0f044d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f0f02d0
com.github.barteksc.pdfviewer.test:id/save_overlay_view = 0x7f08014a
com.github.barteksc.pdfviewer.test:id/save_non_transition_alpha = 0x7f080149
com.github.barteksc.pdfviewer.test:id/row_index_key = 0x7f080148
com.github.barteksc.pdfviewer.test:id/rounded = 0x7f080147
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f0f0259
com.github.barteksc.pdfviewer.test:id/right_side = 0x7f080146
com.github.barteksc.pdfviewer.test:id/right_icon = 0x7f080145
com.github.barteksc.pdfviewer.test:id/right = 0x7f080143
com.github.barteksc.pdfviewer.test:id/report_drawn = 0x7f080141
com.github.barteksc.pdfviewer.test:id/rectangles = 0x7f080140
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Suggestion = 0x7f0f036b
com.github.barteksc.pdfviewer.test:id/ratio = 0x7f08013f
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.MaterialComponents = 0x7f0f017a
com.github.barteksc.pdfviewer.test:id/radio = 0x7f08013e
com.github.barteksc.pdfviewer.test:id/progress_horizontal = 0x7f08013d
com.github.barteksc.pdfviewer.test:id/pressed = 0x7f08013b
com.github.barteksc.pdfviewer.test:id/postLayout = 0x7f08013a
com.github.barteksc.pdfviewer.test:id/pin = 0x7f080138
com.github.barteksc.pdfviewer.test:id/percent = 0x7f080137
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TextButton.Icon = 0x7f0f035a
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Badge = 0x7f0f01f7
com.github.barteksc.pdfviewer.test:id/peekHeight = 0x7f080136
com.github.barteksc.pdfviewer.test:id/path = 0x7f080134
com.github.barteksc.pdfviewer.test:id/password_toggle = 0x7f080133
com.github.barteksc.pdfviewer.test:string/mtrl_timepicker_confirm = 0x7f0e0099
com.github.barteksc.pdfviewer.test:id/parentRelative = 0x7f080131
com.github.barteksc.pdfviewer.test:id/parentPanel = 0x7f080130
com.github.barteksc.pdfviewer.test:id/parent = 0x7f08012f
com.github.barteksc.pdfviewer.test:string/mtrl_picker_confirm = 0x7f0e0071
com.github.barteksc.pdfviewer.test:id/packed = 0x7f08012d
com.github.barteksc.pdfviewer.test:id/outward = 0x7f08012c
com.github.barteksc.pdfviewer.test:id/outline = 0x7f08012b
com.github.barteksc.pdfviewer.test:id/open_search_view_toolbar_container = 0x7f08012a
com.github.barteksc.pdfviewer.test:id/open_search_view_toolbar = 0x7f080129
com.github.barteksc.pdfviewer.test:id/open_search_view_search_prefix = 0x7f080127
com.github.barteksc.pdfviewer.test:id/open_search_view_edit_text = 0x7f080123
com.github.barteksc.pdfviewer.test:id/open_search_view_dummy_toolbar = 0x7f080122
com.github.barteksc.pdfviewer.test:id/open_search_view_divider = 0x7f080121
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00fa
com.github.barteksc.pdfviewer.test:id/open_search_view_content_container = 0x7f080120
com.github.barteksc.pdfviewer.test:id/open_search_view_clear_button = 0x7f08011f
com.github.barteksc.pdfviewer.test:id/off = 0x7f08011b
com.github.barteksc.pdfviewer.test:id/notification_main_column = 0x7f080119
com.github.barteksc.pdfviewer.test:id/normal = 0x7f080117
com.github.barteksc.pdfviewer.test:id/none = 0x7f080116
com.github.barteksc.pdfviewer.test:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f00bd
com.github.barteksc.pdfviewer.test:id/noScroll = 0x7f080115
com.github.barteksc.pdfviewer.test:id/navigation_header_container = 0x7f080113
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f0f0370
com.github.barteksc.pdfviewer.test:id/navigation_bar_item_large_label_view = 0x7f080111
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0168
com.github.barteksc.pdfviewer.test:id/navigation_bar_item_labels_group = 0x7f080110
com.github.barteksc.pdfviewer.test:id/navigation_bar_item_icon_container = 0x7f08010e
com.github.barteksc.pdfviewer.test:id/mtrl_picker_title_text = 0x7f08010a
com.github.barteksc.pdfviewer.test:id/mtrl_picker_text_input_range_end = 0x7f080108
com.github.barteksc.pdfviewer.test:id/mtrl_picker_text_input_date = 0x7f080107
com.github.barteksc.pdfviewer.test:id/mtrl_picker_header_toggle = 0x7f080106
com.github.barteksc.pdfviewer.test:id/mtrl_picker_header_selection_text = 0x7f080104
com.github.barteksc.pdfviewer.test:id/mtrl_internal_children_alpha_tag = 0x7f080100
com.github.barteksc.pdfviewer.test:styleable/BottomNavigationView = 0x7f100016
com.github.barteksc.pdfviewer.test:id/mtrl_card_checked_layer_id = 0x7f0800fe
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_year_selector_frame = 0x7f0800fd
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_text_input_frame = 0x7f0800fc
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_selection_frame = 0x7f0800fb
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_months = 0x7f0800fa
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_main_pane = 0x7f0800f9
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c0160
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_frame = 0x7f0800f8
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_days_of_week = 0x7f0800f7
com.github.barteksc.pdfviewer.test:styleable/Layout = 0x7f100044
com.github.barteksc.pdfviewer.test:id/mtrl_anchor_parent = 0x7f0800f5
com.github.barteksc.pdfviewer.test:id/month_navigation_previous = 0x7f0800f2
com.github.barteksc.pdfviewer.test:id/month_navigation_next = 0x7f0800f1
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
com.github.barteksc.pdfviewer.test:id/month_navigation_fragment_toggle = 0x7f0800f0
com.github.barteksc.pdfviewer.test:id/month_navigation_bar = 0x7f0800ef
com.github.barteksc.pdfviewer.test:id/month_grid = 0x7f0800ee
com.github.barteksc.pdfviewer.test:id/mini = 0x7f0800ed
com.github.barteksc.pdfviewer.test:id/middle = 0x7f0800ec
com.github.barteksc.pdfviewer.test:id/matrix = 0x7f0800ea
com.github.barteksc.pdfviewer.test:id/material_value_index = 0x7f0800e9
com.github.barteksc.pdfviewer.test:style/AlertDialog.AppCompat.Light = 0x7f0f0001
com.github.barteksc.pdfviewer.test:id/material_timepicker_view = 0x7f0800e8
com.github.barteksc.pdfviewer.test:id/material_timepicker_ok_button = 0x7f0800e7
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f0018
com.github.barteksc.pdfviewer.test:id/material_timepicker_mode_button = 0x7f0800e6
com.github.barteksc.pdfviewer.test:id/material_timepicker_container = 0x7f0800e5
com.github.barteksc.pdfviewer.test:id/material_timepicker_cancel_button = 0x7f0800e4
com.github.barteksc.pdfviewer.test:id/material_minute_tv = 0x7f0800e2
com.github.barteksc.pdfviewer.test:id/material_minute_text_input = 0x7f0800e1
com.github.barteksc.pdfviewer.test:styleable/KeyTimeCycle = 0x7f100042
com.github.barteksc.pdfviewer.test:id/material_hour_tv = 0x7f0800df
com.github.barteksc.pdfviewer.test:integer/mtrl_chip_anim_duration = 0x7f090035
com.github.barteksc.pdfviewer.test:id/material_hour_text_input = 0x7f0800de
com.github.barteksc.pdfviewer.test:id/material_clock_period_toggle = 0x7f0800dd
com.github.barteksc.pdfviewer.test:id/material_clock_period_pm_button = 0x7f0800dc
com.github.barteksc.pdfviewer.test:id/material_clock_period_am_button = 0x7f0800db
com.github.barteksc.pdfviewer.test:id/material_clock_hand = 0x7f0800d9
com.github.barteksc.pdfviewer.test:id/material_clock_display = 0x7f0800d6
com.github.barteksc.pdfviewer.test:id/match_parent = 0x7f0800d5
com.github.barteksc.pdfviewer.test:id/masked = 0x7f0800d4
com.github.barteksc.pdfviewer.test:id/line3 = 0x7f0800ce
com.github.barteksc.pdfviewer.test:id/line1 = 0x7f0800cd
com.github.barteksc.pdfviewer.test:id/jumpToStart = 0x7f0800c7
com.github.barteksc.pdfviewer.test:id/jumpToEnd = 0x7f0800c6
com.github.barteksc.pdfviewer.test:id/italic = 0x7f0800c4
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0148
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_container_highest = 0x7f050166
com.github.barteksc.pdfviewer.test:id/invisible = 0x7f0800c2
com.github.barteksc.pdfviewer.test:id/info = 0x7f0800c1
com.github.barteksc.pdfviewer.test:id/scrollable = 0x7f080152
com.github.barteksc.pdfviewer.test:id/ignore = 0x7f0800bd
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f0f0424
com.github.barteksc.pdfviewer.test:id/icon_group = 0x7f0800bb
com.github.barteksc.pdfviewer.test:attr/actionBarSize = 0x7f030003
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_error_container = 0x7f0501bd
com.github.barteksc.pdfviewer.test:id/icon = 0x7f0800ba
com.github.barteksc.pdfviewer.test:id/honorRequest = 0x7f0800b9
com.github.barteksc.pdfviewer.test:attr/showPaths = 0x7f030388
com.github.barteksc.pdfviewer.test:id/groups = 0x7f0800b4
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f070019
com.github.barteksc.pdfviewer.test:id/group_divider = 0x7f0800b3
com.github.barteksc.pdfviewer.test:id/graph_wrap = 0x7f0800b2
com.github.barteksc.pdfviewer.test:id/fill = 0x7f08009f
com.github.barteksc.pdfviewer.test:id/ghost_view_holder = 0x7f0800af
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f060179
com.github.barteksc.pdfviewer.test:id/ghost_view = 0x7f0800ae
com.github.barteksc.pdfviewer.test:id/fitCenter = 0x7f0800a3
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f0f03e0
com.github.barteksc.pdfviewer.test:id/fill_vertical = 0x7f0800a1
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_primary = 0x7f05019a
com.github.barteksc.pdfviewer.test:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f06029c
com.github.barteksc.pdfviewer.test:id/expand_activities_button = 0x7f08009c
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f0601f9
com.github.barteksc.pdfviewer.test:id/exitUntilCollapsed = 0x7f08009b
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Dialog.Alert = 0x7f0f0215
com.github.barteksc.pdfviewer.test:id/open_search_bar_text_view = 0x7f08011d
com.github.barteksc.pdfviewer.test:id/end = 0x7f080097
com.github.barteksc.pdfviewer.test:id/view_tree_view_model_store_owner = 0x7f0801b7
com.github.barteksc.pdfviewer.test:id/edit_query = 0x7f080094
com.github.barteksc.pdfviewer.test:id/dragUp = 0x7f08008e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Snackbar.TextView = 0x7f0f03ca
com.github.barteksc.pdfviewer.test:id/dragLeft = 0x7f08008b
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f0f018f
com.github.barteksc.pdfviewer.test:id/dragEnd = 0x7f08008a
com.github.barteksc.pdfviewer.test:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0109
com.github.barteksc.pdfviewer.test:attr/checkedIconEnabled = 0x7f0300a4
com.github.barteksc.pdfviewer.test:id/dragDown = 0x7f080089
com.github.barteksc.pdfviewer.test:id/disablePostScroll = 0x7f080086
com.github.barteksc.pdfviewer.test:attr/placeholder_emptyVisibility = 0x7f030347
com.github.barteksc.pdfviewer.test:id/disableHome = 0x7f080085
com.github.barteksc.pdfviewer.test:attr/pathMotionArc = 0x7f03033c
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500a3
com.github.barteksc.pdfviewer.test:id/direct = 0x7f080084
com.github.barteksc.pdfviewer.test:attr/title = 0x7f030438
com.github.barteksc.pdfviewer.test:id/dialog_button = 0x7f080082
com.github.barteksc.pdfviewer.test:dimen/m3_divider_heavy_thickness = 0x7f0601a8
com.github.barteksc.pdfviewer.test:color/m3_switch_thumb_tint = 0x7f050149
com.github.barteksc.pdfviewer.test:id/design_navigation_view = 0x7f080081
com.github.barteksc.pdfviewer.test:id/design_menu_item_action_area_stub = 0x7f08007f
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f01b1
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.github.barteksc.pdfviewer.test:id/default_activity_button = 0x7f08007b
com.github.barteksc.pdfviewer.test:id/action_mode_bar_stub = 0x7f08003e
com.github.barteksc.pdfviewer.test:id/cut = 0x7f080076
com.github.barteksc.pdfviewer.test:id/custom = 0x7f080074
com.github.barteksc.pdfviewer.test:id/cos = 0x7f080071
com.github.barteksc.pdfviewer.test:id/contiguous = 0x7f08006f
com.github.barteksc.pdfviewer.test:dimen/notification_large_icon_width = 0x7f060309
com.github.barteksc.pdfviewer.test:id/content = 0x7f08006d
com.github.barteksc.pdfviewer.test:attr/layout_constraintGuide_end = 0x7f03025f
com.github.barteksc.pdfviewer.test:id/clip_vertical = 0x7f080067
com.github.barteksc.pdfviewer.test:attr/flow_verticalBias = 0x7f0301dc
com.github.barteksc.pdfviewer.test:id/chronometer = 0x7f080063
com.github.barteksc.pdfviewer.test:id/center_horizontal = 0x7f08005d
com.github.barteksc.pdfviewer.test:id/center = 0x7f08005a
com.github.barteksc.pdfviewer.test:id/buttonPanel = 0x7f080058
com.github.barteksc.pdfviewer.test:id/bounce = 0x7f080057
com.github.barteksc.pdfviewer.test:attr/materialClockStyle = 0x7f0302c5
com.github.barteksc.pdfviewer.test:id/bottom = 0x7f080056
com.github.barteksc.pdfviewer.test:attr/tabIconTintMode = 0x7f0303ce
com.github.barteksc.pdfviewer.test:id/beginOnFirstDraw = 0x7f080053
com.github.barteksc.pdfviewer.test:id/barrier = 0x7f080051
com.github.barteksc.pdfviewer.test:id/autoCompleteToEnd = 0x7f08004f
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0056
com.github.barteksc.pdfviewer.test:integer/mtrl_view_invisible = 0x7f090040
com.github.barteksc.pdfviewer.test:id/animateToEnd = 0x7f080048
com.github.barteksc.pdfviewer.test:id/always = 0x7f080047
com.github.barteksc.pdfviewer.test:id/aligned = 0x7f080045
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
com.github.barteksc.pdfviewer.test:id/add = 0x7f080043
com.github.barteksc.pdfviewer.test:id/activity_chooser_view_content = 0x7f080042
com.github.barteksc.pdfviewer.test:id/action_text = 0x7f080040
com.github.barteksc.pdfviewer.test:id/action_menu_presenter = 0x7f08003c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f0f03bc
com.github.barteksc.pdfviewer.test:id/action_menu_divider = 0x7f08003b
com.github.barteksc.pdfviewer.test:id/action_context_bar = 0x7f080038
com.github.barteksc.pdfviewer.test:id/action_bar_title = 0x7f080036
com.github.barteksc.pdfviewer.test:id/action_bar_container = 0x7f080032
com.github.barteksc.pdfviewer.test:id/action_bar_activity_content = 0x7f080031
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Surface = 0x7f0f038c
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_7 = 0x7f08002d
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_6 = 0x7f08002c
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_5 = 0x7f08002b
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_31 = 0x7f080029
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_29 = 0x7f080026
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error20 = 0x7f0500ec
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_21 = 0x7f08001e
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Button.Small = 0x7f0f00d2
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_18 = 0x7f08001a
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_17 = 0x7f080019
com.github.barteksc.pdfviewer.test:id/tag_on_apply_window_listener = 0x7f080185
com.github.barteksc.pdfviewer.test:attr/theme = 0x7f03041e
com.github.barteksc.pdfviewer.test:id/action_mode_close_button = 0x7f08003f
com.github.barteksc.pdfviewer.test:anim/abc_slide_out_bottom = 0x7f010008
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_12 = 0x7f080014
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_11 = 0x7f080013
com.github.barteksc.pdfviewer.test:attr/brightness = 0x7f030082
com.github.barteksc.pdfviewer.test:id/TOP_START = 0x7f08000d
com.github.barteksc.pdfviewer.test:id/TOP_END = 0x7f08000c
com.github.barteksc.pdfviewer.test:attr/itemFillColor = 0x7f030222
com.github.barteksc.pdfviewer.test:id/SHOW_PATH = 0x7f080009
com.github.barteksc.pdfviewer.test:attr/closeIcon = 0x7f0300c7
com.github.barteksc.pdfviewer.test:id/META = 0x7f080005
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_background = 0x7f05018b
com.github.barteksc.pdfviewer.test:id/FUNCTION = 0x7f080004
com.github.barteksc.pdfviewer.test:drawable/abc_text_select_handle_left_mtrl = 0x7f07006e
com.github.barteksc.pdfviewer.test:drawable/tooltip_frame_dark = 0x7f0700e5
com.github.barteksc.pdfviewer.test:drawable/notification_template_icon_bg = 0x7f0700e0
com.github.barteksc.pdfviewer.test:drawable/test_level_drawable = 0x7f0700e4
com.github.barteksc.pdfviewer.test:drawable/notification_template_icon_low_bg = 0x7f0700e1
com.github.barteksc.pdfviewer.test:drawable/notification_icon_background = 0x7f0700df
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_track = 0x7f0700d4
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_extra_long1 = 0x7f09000f
com.github.barteksc.pdfviewer.test:attr/logo = 0x7f0302a0
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_unchecked = 0x7f0700d1
com.github.barteksc.pdfviewer.test:color/m3_card_stroke_color = 0x7f05006c
com.github.barteksc.pdfviewer.test:id/edge = 0x7f080093
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f0f01df
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700cf
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_pressed = 0x7f0700ce
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700cc
com.github.barteksc.pdfviewer.test:id/transition_position = 0x7f0801aa
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_checked = 0x7f0700cb
com.github.barteksc.pdfviewer.test:color/design_default_color_primary_dark = 0x7f050046
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_secondary_container = 0x7f050161
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb = 0x7f0700ca
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c008c
com.github.barteksc.pdfviewer.test:id/async = 0x7f08004c
com.github.barteksc.pdfviewer.test:drawable/mtrl_popupmenu_background_overlay = 0x7f0700c9
com.github.barteksc.pdfviewer.test:attr/layout_constraintHorizontal_weight = 0x7f030267
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_indeterminate = 0x7f0700c6
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f030f
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_checkbox_checked = 0x7f0700c3
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Button.Borderless = 0x7f0f02f1
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060115
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_cancel = 0x7f0700c1
com.github.barteksc.pdfviewer.test:style/Widget.Design.NavigationView = 0x7f0f0335
com.github.barteksc.pdfviewer.test:attr/state_indeterminate = 0x7f0303ae
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_arrow_drop_up = 0x7f0700c0
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f0f039d
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700bc
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary10 = 0x7f050129
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700ba
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f06016f
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700b9
com.github.barteksc.pdfviewer.test:id/expanded_menu = 0x7f08009d
com.github.barteksc.pdfviewer.test:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
com.github.barteksc.pdfviewer.test:attr/colorOnSecondaryContainer = 0x7f0300ef
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700b7
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700b6
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon = 0x7f0700b5
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialDivider = 0x7f0f042c
com.github.barteksc.pdfviewer.test:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700b2
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f02fa
com.github.barteksc.pdfviewer.test:integer/material_motion_duration_long_1 = 0x7f090026
com.github.barteksc.pdfviewer.test:id/month_title = 0x7f0800f3
com.github.barteksc.pdfviewer.test:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700b1
com.github.barteksc.pdfviewer.test:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700af
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f0f0070
com.github.barteksc.pdfviewer.test:attr/materialCalendarMonthNavigationButton = 0x7f0302bc
com.github.barteksc.pdfviewer.test:drawable/m3_tabs_transparent_background = 0x7f0700a7
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0041
com.github.barteksc.pdfviewer.test:drawable/m3_tabs_background = 0x7f0700a4
com.github.barteksc.pdfviewer.test:style/Widget.Design.Snackbar = 0x7f0f0337
com.github.barteksc.pdfviewer.test:dimen/design_tab_text_size = 0x7f06008b
com.github.barteksc.pdfviewer.test:drawable/m3_radiobutton_ripple = 0x7f0700a2
com.github.barteksc.pdfviewer.test:attr/motionEasingStandardInterpolator = 0x7f03030c
com.github.barteksc.pdfviewer.test:id/dragStart = 0x7f08008d
com.github.barteksc.pdfviewer.test:drawable/ic_mtrl_chip_checked_black = 0x7f070098
com.github.barteksc.pdfviewer.test:drawable/ic_mtrl_checked_circle = 0x7f070097
com.github.barteksc.pdfviewer.test:drawable/abc_switch_track_mtrl_alpha = 0x7f07006a
com.github.barteksc.pdfviewer.test:drawable/ic_m3_chip_checked_circle = 0x7f070095
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_inset = 0x7f0602cd
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadline6 = 0x7f0303fb
com.github.barteksc.pdfviewer.test:color/m3_efab_ripple_color_selector = 0x7f050085
com.github.barteksc.pdfviewer.test:drawable/ic_m3_chip_check = 0x7f070094
com.github.barteksc.pdfviewer.test:id/spread = 0x7f08016f
com.github.barteksc.pdfviewer.test:drawable/ic_clear_black_24 = 0x7f070091
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral80 = 0x7f050105
com.github.barteksc.pdfviewer.test:drawable/ic_call_decline_low = 0x7f070090
com.github.barteksc.pdfviewer.test:drawable/ic_call_answer = 0x7f07008b
com.github.barteksc.pdfviewer.test:drawable/design_ic_visibility = 0x7f070086
com.github.barteksc.pdfviewer.test:attr/tabBackground = 0x7f0303ca
com.github.barteksc.pdfviewer.test:drawable/m3_tabs_line_indicator = 0x7f0700a5
com.github.barteksc.pdfviewer.test:drawable/default_scroll_handle_right = 0x7f070083
com.github.barteksc.pdfviewer.test:id/mtrl_picker_text_input_range_start = 0x7f080109
com.github.barteksc.pdfviewer.test:drawable/default_scroll_handle_bottom = 0x7f070081
com.github.barteksc.pdfviewer.test:attr/collapsingToolbarLayoutStyle = 0x7f0300d9
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_secondary = 0x7f05003b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant100 = 0x7f050110
com.github.barteksc.pdfviewer.test:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070080
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06012e
com.github.barteksc.pdfviewer.test:drawable/btn_radio_off_mtrl = 0x7f07007d
com.github.barteksc.pdfviewer.test:attr/activeIndicatorLabelPadding = 0x7f030024
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_top_padding = 0x7f0602ab
com.github.barteksc.pdfviewer.test:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070073
com.github.barteksc.pdfviewer.test:color/material_personalized_color_primary_text = 0x7f050272
com.github.barteksc.pdfviewer.test:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070071
com.github.barteksc.pdfviewer.test:drawable/abc_text_select_handle_middle_mtrl = 0x7f07006f
com.github.barteksc.pdfviewer.test:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700ac
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f0f03fa
com.github.barteksc.pdfviewer.test:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f0f03b9
com.github.barteksc.pdfviewer.test:attr/motionDurationShort2 = 0x7f0302fe
com.github.barteksc.pdfviewer.test:id/dimensions = 0x7f080083
com.github.barteksc.pdfviewer.test:drawable/abc_star_half_black_48dp = 0x7f070068
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0058
com.github.barteksc.pdfviewer.test:drawable/abc_spinner_textfield_background_material = 0x7f070066
com.github.barteksc.pdfviewer.test:string/mtrl_picker_announce_current_range_selection = 0x7f0e006d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f060186
com.github.barteksc.pdfviewer.test:id/design_bottom_sheet = 0x7f08007d
com.github.barteksc.pdfviewer.test:drawable/abc_spinner_mtrl_am_alpha = 0x7f070065
com.github.barteksc.pdfviewer.test:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f
com.github.barteksc.pdfviewer.test:drawable/abc_ratingbar_material = 0x7f07005b
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f0f0075
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f060206
com.github.barteksc.pdfviewer.test:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070058
com.github.barteksc.pdfviewer.test:id/textStart = 0x7f080193
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602d7
com.github.barteksc.pdfviewer.test:drawable/m3_appbar_background = 0x7f07009c
com.github.barteksc.pdfviewer.test:attr/windowActionBarOverlay = 0x7f030479
com.github.barteksc.pdfviewer.test:drawable/abc_list_selector_holo_light = 0x7f070057
com.github.barteksc.pdfviewer.test:drawable/abc_list_selector_disabled_holo_light = 0x7f070055
com.github.barteksc.pdfviewer.test:drawable/abc_list_selector_background_transition_holo_light = 0x7f070053
com.github.barteksc.pdfviewer.test:drawable/abc_list_longpressed_holo = 0x7f07004f
com.github.barteksc.pdfviewer.test:drawable/abc_list_focused_holo = 0x7f07004e
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f0f0164
com.github.barteksc.pdfviewer.test:drawable/abc_list_divider_material = 0x7f07004c
com.github.barteksc.pdfviewer.test:attr/colorOnPrimarySurface = 0x7f0300ed
com.github.barteksc.pdfviewer.test:drawable/abc_item_background_holo_light = 0x7f07004b
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0042
com.github.barteksc.pdfviewer.test:drawable/abc_ic_voice_search_api_material = 0x7f070049
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Overline = 0x7f0f0203
com.github.barteksc.pdfviewer.test:drawable/abc_ic_search_api_material = 0x7f070048
com.github.barteksc.pdfviewer.test:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070047
com.github.barteksc.pdfviewer.test:id/disableScroll = 0x7f080087
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700b4
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_container_color = 0x7f0c004c
com.github.barteksc.pdfviewer.test:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f06017e
com.github.barteksc.pdfviewer.test:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003e
com.github.barteksc.pdfviewer.test:drawable/abc_control_background_material = 0x7f07003a
com.github.barteksc.pdfviewer.test:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070039
com.github.barteksc.pdfviewer.test:styleable/StateListDrawableItem = 0x7f10007f
com.github.barteksc.pdfviewer.test:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.github.barteksc.pdfviewer.test:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070036
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0310
com.github.barteksc.pdfviewer.test:drawable/abc_btn_radio_material_anim = 0x7f070032
com.github.barteksc.pdfviewer.test:attr/layout_constraintVertical_bias = 0x7f030274
com.github.barteksc.pdfviewer.test:drawable/abc_btn_radio_material = 0x7f070031
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f0f008c
com.github.barteksc.pdfviewer.test:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002e
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_container_color = 0x7f0c00f2
com.github.barteksc.pdfviewer.test:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002d
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500e5
com.github.barteksc.pdfviewer.test:drawable/abc_btn_check_material = 0x7f07002b
com.github.barteksc.pdfviewer.test:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070028
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary0 = 0x7f050128
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f060203
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070027
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070025
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070024
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070022
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070021
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070020
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral20 = 0x7f0500fb
com.github.barteksc.pdfviewer.test:color/m3_card_foreground_color = 0x7f05006a
com.github.barteksc.pdfviewer.test:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600c8
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001e
com.github.barteksc.pdfviewer.test:attr/behavior_autoHide = 0x7f030063
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Slider = 0x7f0f0439
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.github.barteksc.pdfviewer.test:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070017
com.github.barteksc.pdfviewer.test:color/m3_textfield_stroke_color = 0x7f0501f0
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070015
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f0026
com.github.barteksc.pdfviewer.test:drawable/abc_ic_clear_material = 0x7f07003f
com.github.barteksc.pdfviewer.test:styleable/ConstraintLayout_Layout = 0x7f100026
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070014
com.github.barteksc.pdfviewer.test:drawable/design_snackbar_background = 0x7f070089
com.github.barteksc.pdfviewer.test:attr/searchHintIcon = 0x7f03036d
com.github.barteksc.pdfviewer.test:attr/motionDurationLong3 = 0x7f0302f7
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070012
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070010
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f07000f
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000e
com.github.barteksc.pdfviewer.test:id/inward = 0x7f0800c3
com.github.barteksc.pdfviewer.test:drawable/$m3_avd_show_password__2 = 0x7f07000b
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070016
com.github.barteksc.pdfviewer.test:attr/layout_keyline = 0x7f030285
com.github.barteksc.pdfviewer.test:drawable/$m3_avd_show_password__1 = 0x7f07000a
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f01c0
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_action_inline_max_width = 0x7f06007e
com.github.barteksc.pdfviewer.test:drawable/$avd_hide_password__2 = 0x7f070002
com.github.barteksc.pdfviewer.test:dimen/tooltip_y_offset_touch = 0x7f06031a
com.github.barteksc.pdfviewer.test:styleable/AppCompatTheme = 0x7f100012
com.github.barteksc.pdfviewer.test:attr/badgeWidePadding = 0x7f030058
com.github.barteksc.pdfviewer.test:id/accelerate = 0x7f08000e
com.github.barteksc.pdfviewer.test:dimen/tooltip_precise_anchor_threshold = 0x7f060317
com.github.barteksc.pdfviewer.test:dimen/tooltip_precise_anchor_extra_offset = 0x7f060316
com.github.barteksc.pdfviewer.test:id/never = 0x7f080114
com.github.barteksc.pdfviewer.test:dimen/tooltip_horizontal_padding = 0x7f060314
com.github.barteksc.pdfviewer.test:id/easeInOut = 0x7f080091
com.github.barteksc.pdfviewer.test:dimen/tooltip_corner_radius = 0x7f060313
com.github.barteksc.pdfviewer.test:dimen/notification_top_pad_large_text = 0x7f060312
com.github.barteksc.pdfviewer.test:id/standard = 0x7f080175
com.github.barteksc.pdfviewer.test:dimen/notification_top_pad = 0x7f060311
com.github.barteksc.pdfviewer.test:attr/flow_firstVerticalStyle = 0x7f0301d0
com.github.barteksc.pdfviewer.test:id/baseline = 0x7f080052
com.github.barteksc.pdfviewer.test:dimen/notification_right_side_padding_top = 0x7f06030d
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_legacy_decelerate = 0x7f0e0041
com.github.barteksc.pdfviewer.test:attr/endIconTint = 0x7f03018d
com.github.barteksc.pdfviewer.test:color/m3_dynamic_dark_default_color_secondary_text = 0x7f05007c
com.github.barteksc.pdfviewer.test:dimen/notification_right_icon_size = 0x7f06030c
com.github.barteksc.pdfviewer.test:attr/colorPrimaryFixed = 0x7f0300fe
com.github.barteksc.pdfviewer.test:drawable/ic_arrow_back_black_24 = 0x7f07008a
com.github.barteksc.pdfviewer.test:color/m3_simple_item_ripple_color = 0x7f050144
com.github.barteksc.pdfviewer.test:dimen/notification_large_icon_height = 0x7f060308
com.github.barteksc.pdfviewer.test:dimen/notification_big_circle_margin = 0x7f060306
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f7
com.github.barteksc.pdfviewer.test:dimen/notification_action_text_size = 0x7f060305
com.github.barteksc.pdfviewer.test:dimen/mtrl_tooltip_padding = 0x7f060302
com.github.barteksc.pdfviewer.test:attr/colorSurfaceBright = 0x7f030109
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f0602fb
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_emphasized = 0x7f0e003b
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_end_icon_margin_start = 0x7f0602fa
com.github.barteksc.pdfviewer.test:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009b
com.github.barteksc.pdfviewer.test:drawable/default_scroll_handle_left = 0x7f070082
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_counter_margin_start = 0x7f0602f9
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f0f026c
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f0602f8
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0602f7
com.github.barteksc.pdfviewer.test:color/m3_sys_color_primary_fixed_dim = 0x7f0501de
com.github.barteksc.pdfviewer.test:drawable/$avd_show_password__1 = 0x7f070004
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c00a1
com.github.barteksc.pdfviewer.test:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
com.github.barteksc.pdfviewer.test:dimen/design_navigation_icon_padding = 0x7f060076
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0602f6
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_variant = 0x7f05016a
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602f4
com.github.barteksc.pdfviewer.test:dimen/mtrl_switch_track_width = 0x7f0602f3
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f0f040e
com.github.barteksc.pdfviewer.test:attr/tabIndicatorGravity = 0x7f0303d4
com.github.barteksc.pdfviewer.test:dimen/mtrl_switch_track_height = 0x7f0602f2
com.github.barteksc.pdfviewer.test:attr/layout_constraintBottom_creator = 0x7f030255
com.github.barteksc.pdfviewer.test:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070052
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b5
com.github.barteksc.pdfviewer.test:dimen/mtrl_switch_thumb_elevation = 0x7f0602ef
com.github.barteksc.pdfviewer.test:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602ed
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.FloatingActionButton = 0x7f0f0108
com.github.barteksc.pdfviewer.test:attr/progressBarPadding = 0x7f030351
com.github.barteksc.pdfviewer.test:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602ec
com.github.barteksc.pdfviewer.test:id/text_input_start_icon = 0x7f080197
com.github.barteksc.pdfviewer.test:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602ea
com.github.barteksc.pdfviewer.test:drawable/abc_popup_background_mtrl_mult = 0x7f070059
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_widget_height = 0x7f0602e7
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f0f02c8
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_track_side_padding = 0x7f0602e6
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f007a
com.github.barteksc.pdfviewer.test:id/selection_type = 0x7f08015f
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_tick_radius = 0x7f0602e4
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_thumb_elevation = 0x7f0602e2
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00be
com.github.barteksc.pdfviewer.test:id/indeterminate = 0x7f0800c0
com.github.barteksc.pdfviewer.test:id/easeOut = 0x7f080092
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_label_square_side = 0x7f0602e1
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Body.Text = 0x7f0f0124
com.github.barteksc.pdfviewer.test:drawable/ic_call_answer_video_low = 0x7f07008e
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_label_radius = 0x7f0602e0
com.github.barteksc.pdfviewer.test:drawable/m3_tabs_rounded_line_indicator = 0x7f0700a6
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f0f029b
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_track_thickness = 0x7f0602da
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0053
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602d9
com.github.barteksc.pdfviewer.test:attr/behavior_fitToContents = 0x7f030067
com.github.barteksc.pdfviewer.test:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010b
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_size_small = 0x7f0602d5
com.github.barteksc.pdfviewer.test:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_size = 0x7f0602d2
com.github.barteksc.pdfviewer.test:color/mtrl_choice_chip_background_color = 0x7f0502a8
com.github.barteksc.pdfviewer.test:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f06022f
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602ce
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_variant = 0x7f050281
com.github.barteksc.pdfviewer.test:id/confirm_button = 0x7f08006b
com.github.barteksc.pdfviewer.test:attr/checkedState = 0x7f0300aa
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_margin = 0x7f0602ca
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_elevation = 0x7f0602c7
com.github.barteksc.pdfviewer.test:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602c3
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602c2
com.github.barteksc.pdfviewer.test:attr/state_lifted = 0x7f0303b0
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_item_icon_padding = 0x7f0602c0
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602bc
com.github.barteksc.pdfviewer.test:dimen/mtrl_min_touch_target_size = 0x7f0602bb
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f0f008b
com.github.barteksc.pdfviewer.test:attr/indicatorInset = 0x7f030218
com.github.barteksc.pdfviewer.test:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602ba
com.github.barteksc.pdfviewer.test:layout/design_navigation_item = 0x7f0b0024
com.github.barteksc.pdfviewer.test:dimen/mtrl_low_ripple_default_alpha = 0x7f0602b7
com.github.barteksc.pdfviewer.test:dimen/mtrl_fab_translation_z_pressed = 0x7f0602b2
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f07001f
com.github.barteksc.pdfviewer.test:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602b1
com.github.barteksc.pdfviewer.test:dimen/mtrl_fab_min_touch_target = 0x7f0602b0
com.github.barteksc.pdfviewer.test:attr/listPreferredItemHeightSmall = 0x7f03029b
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_start_padding = 0x7f0602a9
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_min_width = 0x7f0602a8
com.github.barteksc.pdfviewer.test:string/error_icon_content_description = 0x7f0e002f
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_icon_size = 0x7f0602a5
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602a4
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602a0
com.github.barteksc.pdfviewer.test:attr/materialSearchViewToolbarStyle = 0x7f0302d1
com.github.barteksc.pdfviewer.test:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f06029e
com.github.barteksc.pdfviewer.test:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f06029d
com.github.barteksc.pdfviewer.test:string/path_password_strike_through = 0x7f0e009e
com.github.barteksc.pdfviewer.test:attr/tabMode = 0x7f0303d9
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_month_vertical_padding = 0x7f060283
com.github.barteksc.pdfviewer.test:dimen/mtrl_chip_text_size = 0x7f06029b
com.github.barteksc.pdfviewer.test:attr/titleTextStyle = 0x7f030446
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_surface_variant = 0x7f05026a
com.github.barteksc.pdfviewer.test:dimen/mtrl_chip_pressed_translation_z = 0x7f06029a
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f0f041e
com.github.barteksc.pdfviewer.test:dimen/mtrl_card_dragged_z = 0x7f060297
com.github.barteksc.pdfviewer.test:dimen/mtrl_card_corner_radius = 0x7f060296
com.github.barteksc.pdfviewer.test:dimen/mtrl_card_checked_icon_size = 0x7f060295
com.github.barteksc.pdfviewer.test:dimen/mtrl_card_checked_icon_margin = 0x7f060294
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_year_width = 0x7f060293
com.github.barteksc.pdfviewer.test:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f0f00ab
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_year_height = 0x7f060290
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f060288
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f0f039f
com.github.barteksc.pdfviewer.test:color/abc_tint_btn_checkable = 0x7f050013
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f060287
com.github.barteksc.pdfviewer.test:attr/endIconTintMode = 0x7f03018e
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_navigation_top_padding = 0x7f060286
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f060284
com.github.barteksc.pdfviewer.test:attr/textAppearanceCaption = 0x7f0303f2
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_13 = 0x7f080015
com.github.barteksc.pdfviewer.test:attr/textAppearanceDisplayMedium = 0x7f0303f4
com.github.barteksc.pdfviewer.test:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602b4
com.github.barteksc.pdfviewer.test:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060281
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_landscape_header_width = 0x7f060280
com.github.barteksc.pdfviewer.test:string/mtrl_switch_track_path = 0x7f0e0097
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_height = 0x7f06027a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Filter = 0x7f0f0365
com.github.barteksc.pdfviewer.test:string/searchview_navigation_content_description = 0x7f0e00a2
com.github.barteksc.pdfviewer.test:attr/shortcutMatchRequired = 0x7f030381
com.github.barteksc.pdfviewer.test:attr/searchPrefixText = 0x7f03036f
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_divider_thickness = 0x7f060279
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_container_surface_tint_layer_color = 0x7f0c00f3
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_content_padding = 0x7f060277
com.github.barteksc.pdfviewer.test:color/material_on_primary_emphasis_medium = 0x7f050254
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_days_of_week_height = 0x7f060275
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_day_vertical_padding = 0x7f060273
com.github.barteksc.pdfviewer.test:drawable/abc_ratingbar_small_material = 0x7f07005c
com.github.barteksc.pdfviewer.test:attr/sliderStyle = 0x7f030396
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060271
com.github.barteksc.pdfviewer.test:drawable/abc_tab_indicator_material = 0x7f07006b
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_day_corner = 0x7f06026f
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_content_padding = 0x7f06026e
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_bottom_padding = 0x7f06026d
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_icon_padding = 0x7f0601ba
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_action_height = 0x7f06026b
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f06026a
com.github.barteksc.pdfviewer.test:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070040
com.github.barteksc.pdfviewer.test:attr/forceDefaultNavigationOnClickListener = 0x7f0301ed
com.github.barteksc.pdfviewer.test:attr/contentPaddingLeft = 0x7f030129
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_text_size = 0x7f060268
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_stroke_size = 0x7f060264
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b3
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_tertiary = 0x7f05016b
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_padding_top = 0x7f060261
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_text_btn_icon_padding = 0x7f060265
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_padding_right = 0x7f060260
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_letter_spacing = 0x7f06025c
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_margin_horizontal = 0x7f0601d4
com.github.barteksc.pdfviewer.test:id/gone = 0x7f0800b0
com.github.barteksc.pdfviewer.test:integer/cancel_button_image_alpha = 0x7f090004
com.github.barteksc.pdfviewer.test:dimen/compat_notification_large_icon_max_height = 0x7f06005b
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_icon_padding = 0x7f06025a
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_hovered_z = 0x7f060258
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_radius = 0x7f0602d1
com.github.barteksc.pdfviewer.test:attr/actionBarTabTextStyle = 0x7f030008
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_20 = 0x7f08001d
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_focused_z = 0x7f060257
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_dialog_btn_min_width = 0x7f060253
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Display1 = 0x7f0f0198
com.github.barteksc.pdfviewer.test:attr/errorTextColor = 0x7f03019b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant90 = 0x7f050118
com.github.barteksc.pdfviewer.test:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060250
com.github.barteksc.pdfviewer.test:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f06024d
com.github.barteksc.pdfviewer.test:anim/abc_tooltip_exit = 0x7f01000b
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_with_text_size = 0x7f06024b
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f0f026e
com.github.barteksc.pdfviewer.test:attr/tabIndicatorFullWidth = 0x7f0303d3
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f06024a
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060249
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_text_size = 0x7f060248
com.github.barteksc.pdfviewer.test:color/m3_checkbox_button_tint = 0x7f05006e
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f060245
com.github.barteksc.pdfviewer.test:string/m3_ref_typeface_plain_medium = 0x7f0e0039
com.github.barteksc.pdfviewer.test:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f06024c
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary50 = 0x7f0500c8
com.github.barteksc.pdfviewer.test:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f060243
com.github.barteksc.pdfviewer.test:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060241
com.github.barteksc.pdfviewer.test:attr/subheaderInsetEnd = 0x7f0303b9
com.github.barteksc.pdfviewer.test:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060240
com.github.barteksc.pdfviewer.test:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f10002f
com.github.barteksc.pdfviewer.test:dimen/material_time_picker_minimum_screen_height = 0x7f06023c
com.github.barteksc.pdfviewer.test:attr/useMaterialThemeColors = 0x7f03046b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060175
com.github.barteksc.pdfviewer.test:dimen/material_textinput_min_width = 0x7f06023b
com.github.barteksc.pdfviewer.test:dimen/material_helper_text_default_padding_top = 0x7f060235
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_button_icon_path_name = 0x7f0e0060
com.github.barteksc.pdfviewer.test:string/material_hour_suffix = 0x7f0e004a
com.github.barteksc.pdfviewer.test:dimen/material_emphasis_disabled_background = 0x7f06022c
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f0f0251
com.github.barteksc.pdfviewer.test:dimen/material_divider_thickness = 0x7f06022a
com.github.barteksc.pdfviewer.test:dimen/material_cursor_inset = 0x7f060228
com.github.barteksc.pdfviewer.test:dimen/material_clock_period_toggle_vertical_gap = 0x7f060225
com.github.barteksc.pdfviewer.test:dimen/material_clock_period_toggle_horizontal_gap = 0x7f060224
com.github.barteksc.pdfviewer.test:attr/lastBaselineToBottomHeight = 0x7f030246
com.github.barteksc.pdfviewer.test:dimen/material_clock_period_toggle_height = 0x7f060223
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c0100
com.github.barteksc.pdfviewer.test:dimen/material_clock_hand_stroke_width = 0x7f060221
com.github.barteksc.pdfviewer.test:dimen/design_navigation_elevation = 0x7f060075
com.github.barteksc.pdfviewer.test:dimen/material_clock_hand_padding = 0x7f060220
com.github.barteksc.pdfviewer.test:dimen/material_clock_display_padding = 0x7f06021c
com.github.barteksc.pdfviewer.test:dimen/m3_timepicker_window_elevation = 0x7f060218
com.github.barteksc.pdfviewer.test:attr/gestureInsetBottomIgnored = 0x7f0301f1
com.github.barteksc.pdfviewer.test:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f060214
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f0f0412
com.github.barteksc.pdfviewer.test:layout/material_radial_view_group = 0x7f0b0038
com.github.barteksc.pdfviewer.test:drawable/abc_list_selector_holo_dark = 0x7f070056
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f0f01d7
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_compact_width = 0x7f0602c5
com.github.barteksc.pdfviewer.test:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f060213
com.github.barteksc.pdfviewer.test:attr/materialCalendarTheme = 0x7f0302be
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_26 = 0x7f080023
com.github.barteksc.pdfviewer.test:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f0f0013
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f060212
com.github.barteksc.pdfviewer.test:id/src_over = 0x7f080174
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060211
com.github.barteksc.pdfviewer.test:attr/enableEdgeToEdge = 0x7f030186
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f06020f
com.github.barteksc.pdfviewer.test:attr/tabIndicatorAnimationMode = 0x7f0303d1
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f06020d
com.github.barteksc.pdfviewer.test:attr/startIconMinSize = 0x7f0303a5
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f06020c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f0f033e
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f06020a
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.Icon = 0x7f0f03f4
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060200
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f0f037a
com.github.barteksc.pdfviewer.test:id/action_mode_bar = 0x7f08003d
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c4
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f0601fc
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f050193
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f0601fa
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601f3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Dark = 0x7f0f02c7
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601f2
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f01b3
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral20 = 0x7f050206
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601f1
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601ef
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_track_viewport_width = 0x7f09003d
com.github.barteksc.pdfviewer.test:attr/buttonTint = 0x7f030091
com.github.barteksc.pdfviewer.test:dimen/m3_sys_elevation_level5 = 0x7f0601ee
com.github.barteksc.pdfviewer.test:dimen/m3_sys_elevation_level2 = 0x7f0601eb
com.github.barteksc.pdfviewer.test:dimen/m3_sys_elevation_level1 = 0x7f0601ea
com.github.barteksc.pdfviewer.test:attr/haloColor = 0x7f0301f3
com.github.barteksc.pdfviewer.test:dimen/m3_sys_elevation_level0 = 0x7f0601e9
com.github.barteksc.pdfviewer.test:dimen/m3_slider_thumb_elevation = 0x7f0601e4
com.github.barteksc.pdfviewer.test:dimen/m3_side_sheet_standard_elevation = 0x7f0601df
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f00cc
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_container_color = 0x7f0c006d
com.github.barteksc.pdfviewer.test:dimen/m3_side_sheet_modal_elevation = 0x7f0601de
com.github.barteksc.pdfviewer.test:dimen/m3_side_sheet_margin_detached = 0x7f0601dd
com.github.barteksc.pdfviewer.test:dimen/m3_searchview_height = 0x7f0601dc
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f0f008e
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_padding_start = 0x7f0601d7
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500b5
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601d6
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_height = 0x7f0601d3
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.SearchView = 0x7f0f0313
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_elevation = 0x7f0601d2
com.github.barteksc.pdfviewer.test:color/design_fab_shadow_mid_color = 0x7f05004d
com.github.barteksc.pdfviewer.test:dimen/m3_btn_translation_z_base = 0x7f0600e2
com.github.barteksc.pdfviewer.test:dimen/m3_ripple_hovered_alpha = 0x7f0601cf
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f0f0182
com.github.barteksc.pdfviewer.test:id/clip_horizontal = 0x7f080066
com.github.barteksc.pdfviewer.test:dimen/tooltip_margin = 0x7f060315
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601cc
com.github.barteksc.pdfviewer.test:drawable/$m3_avd_hide_password__0 = 0x7f070006
com.github.barteksc.pdfviewer.test:attr/onNegativeCross = 0x7f030325
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_container_height = 0x7f060117
com.github.barteksc.pdfviewer.test:dimen/m3_timepicker_display_stroke_width = 0x7f060217
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f0f03e6
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a1
com.github.barteksc.pdfviewer.test:dimen/m3_badge_horizontal_offset = 0x7f0600b3
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601c9
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_min_height = 0x7f0601c8
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601c7
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f0601fd
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary40 = 0x7f050120
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_icon_size = 0x7f0601c4
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker = 0x7f0f03aa
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601c1
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight = 0x7f0f022f
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601c0
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.Chip = 0x7f0f0113
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_vertical_padding = 0x7f0601bf
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f0f0454
com.github.barteksc.pdfviewer.test:id/start = 0x7f080176
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_shape_inset_end = 0x7f0601bc
com.github.barteksc.pdfviewer.test:id/clear_text = 0x7f080065
com.github.barteksc.pdfviewer.test:dimen/notification_small_icon_size_as_large = 0x7f06030f
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601bb
com.github.barteksc.pdfviewer.test:id/withinBounds = 0x7f0801bc
com.github.barteksc.pdfviewer.test:dimen/m3_menu_elevation = 0x7f0601b6
com.github.barteksc.pdfviewer.test:dimen/m3_extended_fab_top_padding = 0x7f0601ae
com.github.barteksc.pdfviewer.test:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f060303
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f0f0158
com.github.barteksc.pdfviewer.test:id/transition_scene_layoutid_cache = 0x7f0801ab
com.github.barteksc.pdfviewer.test:dimen/m3_extended_fab_start_padding = 0x7f0601ad
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderCancelButton = 0x7f0302b4
com.github.barteksc.pdfviewer.test:id/ALT = 0x7f080000
com.github.barteksc.pdfviewer.test:color/mtrl_btn_transparent_bg_color = 0x7f05029f
com.github.barteksc.pdfviewer.test:dimen/m3_extended_fab_min_height = 0x7f0601ac
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_year_vertical_padding = 0x7f060292
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f06027f
com.github.barteksc.pdfviewer.test:dimen/m3_extended_fab_icon_padding = 0x7f0601ab
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_selection_line_height = 0x7f06027c
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_horizontal_padding = 0x7f0601b9
com.github.barteksc.pdfviewer.test:id/textinput_suffix_text = 0x7f08019d
com.github.barteksc.pdfviewer.test:dimen/m3_extended_fab_end_padding = 0x7f0601aa
com.github.barteksc.pdfviewer.test:attr/helperTextEnabled = 0x7f0301f8
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f060202
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_item_text_color = 0x7f0502b9
com.github.barteksc.pdfviewer.test:dimen/m3_datepicker_elevation = 0x7f0601a7
com.github.barteksc.pdfviewer.test:id/mtrl_view_tag_bottom_padding = 0x7f08010b
com.github.barteksc.pdfviewer.test:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f06023e
com.github.barteksc.pdfviewer.test:id/coordinator = 0x7f080070
com.github.barteksc.pdfviewer.test:string/material_motion_easing_accelerated = 0x7f0e004d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601a6
com.github.barteksc.pdfviewer.test:id/beginning = 0x7f080054
com.github.barteksc.pdfviewer.test:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060242
com.github.barteksc.pdfviewer.test:attr/nestedScrollable = 0x7f03031f
com.github.barteksc.pdfviewer.test:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601a5
com.github.barteksc.pdfviewer.test:id/material_clock_face = 0x7f0800d8
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f06019e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f06019c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f06019b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_container_elevation = 0x7f06019a
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.github.barteksc.pdfviewer.test:dimen/m3_searchview_divider_size = 0x7f0601da
com.github.barteksc.pdfviewer.test:attr/cornerSizeTopLeft = 0x7f03013b
com.github.barteksc.pdfviewer.test:dimen/m3_card_stroke_width = 0x7f0600ec
com.github.barteksc.pdfviewer.test:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f060198
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_track_width = 0x7f060192
com.github.barteksc.pdfviewer.test:drawable/abc_btn_check_material_anim = 0x7f07002c
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Body1 = 0x7f0f01f8
com.github.barteksc.pdfviewer.test:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f060197
com.github.barteksc.pdfviewer.test:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f060196
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f060194
com.github.barteksc.pdfviewer.test:id/auto = 0x7f08004d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_track_height = 0x7f060191
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_card_icon_size = 0x7f060154
com.github.barteksc.pdfviewer.test:id/enterAlwaysCollapsed = 0x7f08009a
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f5
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_icon_size = 0x7f060064
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060190
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f06018e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_container_lowest = 0x7f0501d2
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error0 = 0x7f0500e9
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601c6
com.github.barteksc.pdfviewer.test:string/side_sheet_accessibility_pane_title = 0x7f0e00a3
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_tertiary_container = 0x7f05026c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f06018d
com.github.barteksc.pdfviewer.test:styleable/Fragment = 0x7f100036
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f06018c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_disabled_track_opacity = 0x7f06018b
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f0f03bb
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f060189
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.LabelMedium = 0x7f0f01ee
com.github.barteksc.pdfviewer.test:dimen/m3_comp_snackbar_container_elevation = 0x7f060183
com.github.barteksc.pdfviewer.test:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060180
com.github.barteksc.pdfviewer.test:string/abc_search_hint = 0x7f0e0012
com.github.barteksc.pdfviewer.test:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f06017f
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0138
com.github.barteksc.pdfviewer.test:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602e9
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f06017d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06017c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060174
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060173
com.github.barteksc.pdfviewer.test:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f060177
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060170
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionButton = 0x7f0f0308
com.github.barteksc.pdfviewer.test:dimen/material_clock_number_text_size = 0x7f060222
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016a
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f0f011c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f060169
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_button_container_elevation = 0x7f060123
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_icon_size = 0x7f0602c9
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f060168
com.github.barteksc.pdfviewer.test:dimen/mtrl_switch_text_padding = 0x7f0602ee
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060165
com.github.barteksc.pdfviewer.test:anim/abc_fade_out = 0x7f010001
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060163
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060162
com.github.barteksc.pdfviewer.test:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f060188
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015d
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f060204
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_text_field_outline_width = 0x7f06015b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f06015a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060157
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_card_outline_width = 0x7f060155
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f00d9
com.github.barteksc.pdfviewer.test:attr/wavePeriod = 0x7f030475
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060153
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f06014b
com.github.barteksc.pdfviewer.test:attr/badgeVerticalPadding = 0x7f030057
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadlineMedium = 0x7f0303fd
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060147
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060145
com.github.barteksc.pdfviewer.test:id/textinput_prefix_text = 0x7f08019c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f060141
com.github.barteksc.pdfviewer.test:id/spread_inside = 0x7f080170
com.github.barteksc.pdfviewer.test:dimen/cardview_compat_inset_shadow = 0x7f060052
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant0 = 0x7f050210
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_z = 0x7f060269
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_days_of_week = 0x7f0b0048
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_container_width = 0x7f060140
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_standard_accelerate = 0x7f0e0044
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013d
com.github.barteksc.pdfviewer.test:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602e8
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013c
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_28 = 0x7f080025
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_container_elevation = 0x7f06013a
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Light = 0x7f0f02a4
com.github.barteksc.pdfviewer.test:dimen/m3_comp_menu_container_elevation = 0x7f060137
com.github.barteksc.pdfviewer.test:dimen/m3_comp_linear_progress_indicator_active_indicator_height = 0x7f060136
com.github.barteksc.pdfviewer.test:layout/abc_search_view = 0x7f0b0019
com.github.barteksc.pdfviewer.test:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060135
com.github.barteksc.pdfviewer.test:attr/minHeight = 0x7f0302e5
com.github.barteksc.pdfviewer.test:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060134
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ProgressIndicator = 0x7f0f0437
com.github.barteksc.pdfviewer.test:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060133
com.github.barteksc.pdfviewer.test:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0106
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_container_height = 0x7f06013b
com.github.barteksc.pdfviewer.test:id/time = 0x7f08019e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_input_chip_container_elevation = 0x7f060131
com.github.barteksc.pdfviewer.test:attr/mock_labelColor = 0x7f0302ed
com.github.barteksc.pdfviewer.test:attr/lStar = 0x7f030241
com.github.barteksc.pdfviewer.test:id/container = 0x7f08006c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06012f
com.github.barteksc.pdfviewer.test:attr/dynamicColorThemeOverlay = 0x7f03017d
com.github.barteksc.pdfviewer.test:id/graph = 0x7f0800b1
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.Slider = 0x7f0f011a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filter_chip_container_height = 0x7f06012c
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f06012a
com.github.barteksc.pdfviewer.test:id/view_tree_saved_state_registry_owner = 0x7f0801b6
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_card_icon_size = 0x7f060129
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060126
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060124
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060122
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Year = 0x7f0f03a4
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f06011a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014c
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060119
com.github.barteksc.pdfviewer.test:dimen/mtrl_card_spacing = 0x7f060299
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_container_elevation = 0x7f060116
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomNavigationView = 0x7f0f03ee
com.github.barteksc.pdfviewer.test:drawable/abc_ic_menu_overflow_material = 0x7f070044
com.github.barteksc.pdfviewer.test:drawable/$m3_avd_show_password__0 = 0x7f070009
com.github.barteksc.pdfviewer.test:layout/material_clock_period_toggle = 0x7f0b0034
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060111
com.github.barteksc.pdfviewer.test:drawable/abc_list_divider_mtrl_alpha = 0x7f07004d
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_corner_radius = 0x7f060252
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_card_container_elevation = 0x7f060152
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f060110
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060209
com.github.barteksc.pdfviewer.test:style/Base.V28.Theme.AppCompat.Light = 0x7f0f00b9
com.github.barteksc.pdfviewer.test:attr/fastScrollHorizontalThumbDrawable = 0x7f0301b9
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06010f
com.github.barteksc.pdfviewer.test:drawable/$avd_hide_password__0 = 0x7f070000
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CollapsingToolbar = 0x7f0f0372
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010d
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0105
com.github.barteksc.pdfviewer.test:dimen/m3_comp_elevated_card_icon_size = 0x7f06010c
com.github.barteksc.pdfviewer.test:styleable/SearchView = 0x7f100075
com.github.barteksc.pdfviewer.test:color/material_blue_grey_800 = 0x7f0501fc
com.github.barteksc.pdfviewer.test:dimen/m3_comp_elevated_button_container_elevation = 0x7f060109
com.github.barteksc.pdfviewer.test:dimen/m3_comp_divider_thickness = 0x7f060108
com.github.barteksc.pdfviewer.test:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060107
com.github.barteksc.pdfviewer.test:dimen/m3_comp_badge_size = 0x7f060100
com.github.barteksc.pdfviewer.test:drawable/abc_vector_test = 0x7f070076
com.github.barteksc.pdfviewer.test:dimen/mtrl_switch_thumb_icon_size = 0x7f0602f0
com.github.barteksc.pdfviewer.test:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0600fe
com.github.barteksc.pdfviewer.test:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fb
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_text_padding = 0x7f06027d
com.github.barteksc.pdfviewer.test:dimen/m3_chip_dragged_translation_z = 0x7f0600f6
com.github.barteksc.pdfviewer.test:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f0
com.github.barteksc.pdfviewer.test:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f0f00ac
com.github.barteksc.pdfviewer.test:dimen/m3_carousel_gone_size = 0x7f0600ef
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderDivider = 0x7f0302b6
com.github.barteksc.pdfviewer.test:color/mtrl_tabs_legacy_text_color_selector = 0x7f0502c7
com.github.barteksc.pdfviewer.test:dimen/m3_carousel_debug_keyline_width = 0x7f0600ed
com.github.barteksc.pdfviewer.test:macro/m3_comp_dialog_headline_type = 0x7f0c0026
com.github.barteksc.pdfviewer.test:dimen/m3_card_elevated_hovered_z = 0x7f0600e9
com.github.barteksc.pdfviewer.test:dimen/m3_card_elevated_elevation = 0x7f0600e8
com.github.barteksc.pdfviewer.test:dimen/m3_card_elevated_dragged_z = 0x7f0600e7
com.github.barteksc.pdfviewer.test:dimen/m3_card_dragged_z = 0x7f0600e5
com.github.barteksc.pdfviewer.test:dimen/m3_btn_translation_z_hovered = 0x7f0600e3
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Button = 0x7f0f0196
com.github.barteksc.pdfviewer.test:dimen/m3_btn_text_btn_padding_right = 0x7f0600e1
com.github.barteksc.pdfviewer.test:attr/animate_relativeTo = 0x7f030031
com.github.barteksc.pdfviewer.test:dimen/m3_btn_text_btn_padding_left = 0x7f0600e0
com.github.barteksc.pdfviewer.test:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600df
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017b
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070013
com.github.barteksc.pdfviewer.test:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600de
com.github.barteksc.pdfviewer.test:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.github.barteksc.pdfviewer.test:dimen/m3_btn_stroke_size = 0x7f0600dd
com.github.barteksc.pdfviewer.test:string/call_notification_incoming_text = 0x7f0e0027
com.github.barteksc.pdfviewer.test:dimen/m3_btn_padding_left = 0x7f0600da
com.github.barteksc.pdfviewer.test:id/action_divider = 0x7f080039
com.github.barteksc.pdfviewer.test:attr/actionBarItemBackground = 0x7f030001
com.github.barteksc.pdfviewer.test:dimen/m3_btn_icon_only_min_width = 0x7f0600d6
com.github.barteksc.pdfviewer.test:dimen/m3_carousel_small_item_size_max = 0x7f0600f1
com.github.barteksc.pdfviewer.test:id/slide = 0x7f080166
com.github.barteksc.pdfviewer.test:id/blocking = 0x7f080055
com.github.barteksc.pdfviewer.test:attr/subtitleTextColor = 0x7f0303c0
com.github.barteksc.pdfviewer.test:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d5
com.github.barteksc.pdfviewer.test:dimen/m3_btn_icon_only_default_size = 0x7f0600d4
com.github.barteksc.pdfviewer.test:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d1
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
com.github.barteksc.pdfviewer.test:dimen/m3_btn_elevation = 0x7f0600d0
com.github.barteksc.pdfviewer.test:dimen/m3_btn_elevated_btn_elevation = 0x7f0600cf
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.SmallComponent = 0x7f0f0178
com.github.barteksc.pdfviewer.test:id/centerInside = 0x7f08005c
com.github.barteksc.pdfviewer.test:attr/navigationRailStyle = 0x7f03031b
com.github.barteksc.pdfviewer.test:dimen/m3_btn_disabled_translation_z = 0x7f0600ce
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.NavigationView = 0x7f0f0432
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.BottomAppBar = 0x7f0f0282
com.github.barteksc.pdfviewer.test:dimen/material_clock_size = 0x7f060227
com.github.barteksc.pdfviewer.test:dimen/m3_btn_disabled_elevation = 0x7f0600cd
com.github.barteksc.pdfviewer.test:attr/colorControlNormal = 0x7f0300e1
com.github.barteksc.pdfviewer.test:dimen/m3_btn_dialog_btn_min_width = 0x7f0600cb
com.github.barteksc.pdfviewer.test:string/mtrl_picker_date_header_title = 0x7f0e0073
com.github.barteksc.pdfviewer.test:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600ca
com.github.barteksc.pdfviewer.test:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c7
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.ActionMode = 0x7f0f03e2
com.github.barteksc.pdfviewer.test:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c6
com.github.barteksc.pdfviewer.test:styleable/ListPopupWindow = 0x7f100048
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f021a
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c2
com.github.barteksc.pdfviewer.test:drawable/mtrl_tabs_default_indicator = 0x7f0700d6
com.github.barteksc.pdfviewer.test:id/material_textinput_timepicker = 0x7f0800e3
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_nav_min_height = 0x7f0600c1
com.github.barteksc.pdfviewer.test:id/hideable = 0x7f0800b6
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602c4
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant0 = 0x7f05010e
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600bf
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error50 = 0x7f0500ef
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600be
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f00ec
com.github.barteksc.pdfviewer.test:dimen/m3_badge_with_text_vertical_offset = 0x7f0600ba
com.github.barteksc.pdfviewer.test:id/SHIFT = 0x7f080007
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Dialog = 0x7f0f0214
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_max_width = 0x7f06025d
com.github.barteksc.pdfviewer.test:dimen/m3_badge_with_text_size = 0x7f0600b9
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014b
com.github.barteksc.pdfviewer.test:dimen/m3_badge_with_text_offset = 0x7f0600b8
com.github.barteksc.pdfviewer.test:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b7
com.github.barteksc.pdfviewer.test:attr/itemMaxLines = 0x7f030228
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary60 = 0x7f0500c9
com.github.barteksc.pdfviewer.test:dimen/m3_badge_vertical_offset = 0x7f0600b6
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500af
com.github.barteksc.pdfviewer.test:id/collapseActionView = 0x7f080069
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f0f01e2
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0063
com.github.barteksc.pdfviewer.test:dimen/m3_badge_size = 0x7f0600b5
com.github.barteksc.pdfviewer.test:drawable/ic_call_answer_low = 0x7f07008c
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b1
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301c5
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600ae
com.github.barteksc.pdfviewer.test:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070033
com.github.barteksc.pdfviewer.test:color/design_error = 0x7f05004b
com.github.barteksc.pdfviewer.test:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f060216
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_error_container = 0x7f0501b7
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600ad
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_size_large = 0x7f0600aa
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600a8
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a3
com.github.barteksc.pdfviewer.test:color/m3_slider_inactive_track_color = 0x7f050147
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_elevation = 0x7f0600a0
com.github.barteksc.pdfviewer.test:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009c
com.github.barteksc.pdfviewer.test:id/alertTitle = 0x7f080044
com.github.barteksc.pdfviewer.test:attr/actionOverflowMenuStyle = 0x7f030020
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602d3
com.github.barteksc.pdfviewer.test:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009a
com.github.barteksc.pdfviewer.test:id/easeIn = 0x7f080090
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_button_outline_width = 0x7f060151
com.github.barteksc.pdfviewer.test:color/mtrl_calendar_item_stroke_color = 0x7f0502a0
com.github.barteksc.pdfviewer.test:dimen/hint_pressed_alpha_material_light = 0x7f060099
com.github.barteksc.pdfviewer.test:dimen/hint_pressed_alpha_material_dark = 0x7f060098
com.github.barteksc.pdfviewer.test:styleable/MaterialSwitch = 0x7f100056
com.github.barteksc.pdfviewer.test:dimen/hint_alpha_material_dark = 0x7f060096
com.github.barteksc.pdfviewer.test:dimen/highlight_alpha_material_light = 0x7f060095
com.github.barteksc.pdfviewer.test:attr/region_widthLessThan = 0x7f03035e
com.github.barteksc.pdfviewer.test:dimen/notification_small_icon_background_padding = 0x7f06030e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060164
com.github.barteksc.pdfviewer.test:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b0044
com.github.barteksc.pdfviewer.test:dimen/highlight_alpha_material_dark = 0x7f060094
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0064
com.github.barteksc.pdfviewer.test:dimen/fastscroll_margin = 0x7f060091
com.github.barteksc.pdfviewer.test:attr/textAppearanceOverline = 0x7f030407
com.github.barteksc.pdfviewer.test:color/m3_sys_color_on_primary_fixed_variant = 0x7f0501d8
com.github.barteksc.pdfviewer.test:dimen/disabled_alpha_material_light = 0x7f06008f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Chip.Entry = 0x7f0f0403
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f0f0295
com.github.barteksc.pdfviewer.test:dimen/disabled_alpha_material_dark = 0x7f06008e
com.github.barteksc.pdfviewer.test:drawable/abc_btn_default_mtrl_shape = 0x7f070030
com.github.barteksc.pdfviewer.test:dimen/design_tab_text_size_2line = 0x7f06008c
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_error_container = 0x7f050153
com.github.barteksc.pdfviewer.test:dimen/design_tab_scrollable_min_width = 0x7f06008a
com.github.barteksc.pdfviewer.test:styleable/SearchBar = 0x7f100074
com.github.barteksc.pdfviewer.test:dimen/design_tab_max_width = 0x7f060089
com.github.barteksc.pdfviewer.test:id/view_tree_lifecycle_owner = 0x7f0801b4
com.github.barteksc.pdfviewer.test:color/background_floating_material_light = 0x7f05001e
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_text_size = 0x7f060088
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_active_track_color = 0x7f0c010c
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_padding_vertical_2lines = 0x7f060087
com.github.barteksc.pdfviewer.test:attr/actionModeSplitBackground = 0x7f03001b
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f0
com.github.barteksc.pdfviewer.test:attr/isLightTheme = 0x7f03021c
com.github.barteksc.pdfviewer.test:attr/compatShadowEnabled = 0x7f030118
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_padding_vertical = 0x7f060086
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_bar_avatar_size = 0x7f06016c
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.SeekBar = 0x7f0f0324
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_max_width = 0x7f060083
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f0f0298
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary30 = 0x7f0500c6
com.github.barteksc.pdfviewer.test:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f3
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_elevation = 0x7f060081
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_variant = 0x7f0501d4
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_action_text_color_alpha = 0x7f06007f
com.github.barteksc.pdfviewer.test:dimen/design_navigation_separator_vertical_padding = 0x7f06007d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f02fc
com.github.barteksc.pdfviewer.test:dimen/material_bottom_sheet_max_width = 0x7f06021a
com.github.barteksc.pdfviewer.test:styleable/ActivityChooserView = 0x7f100005
com.github.barteksc.pdfviewer.test:dimen/design_navigation_item_horizontal_padding = 0x7f060078
com.github.barteksc.pdfviewer.test:attr/textAppearanceListItem = 0x7f030404
com.github.barteksc.pdfviewer.test:attr/telltales_tailScale = 0x7f0303e9
com.github.barteksc.pdfviewer.test:dimen/design_fab_translation_z_pressed = 0x7f060074
com.github.barteksc.pdfviewer.test:color/secondary_text_default_material_dark = 0x7f0502db
com.github.barteksc.pdfviewer.test:dimen/design_fab_size_normal = 0x7f060072
com.github.barteksc.pdfviewer.test:dimen/design_fab_size_mini = 0x7f060071
com.github.barteksc.pdfviewer.test:dimen/design_fab_image_size = 0x7f060070
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_text_size = 0x7f06006a
com.github.barteksc.pdfviewer.test:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f0f0015
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_shadow_height = 0x7f060069
com.github.barteksc.pdfviewer.test:attr/hideNavigationIcon = 0x7f0301fd
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_label_padding = 0x7f060067
com.github.barteksc.pdfviewer.test:dimen/m3_ripple_pressed_alpha = 0x7f0601d0
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_item_max_width = 0x7f060065
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_track_decoration = 0x7f0700d5
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_card_container_elevation = 0x7f060125
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_height = 0x7f060063
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_elevation = 0x7f060062
com.github.barteksc.pdfviewer.test:attr/materialAlertDialogBodyTextStyle = 0x7f0302a8
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001a
com.github.barteksc.pdfviewer.test:dimen/design_navigation_item_vertical_padding = 0x7f06007a
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_active_text_size = 0x7f060061
com.github.barteksc.pdfviewer.test:dimen/design_appbar_elevation = 0x7f06005e
com.github.barteksc.pdfviewer.test:id/sawtooth = 0x7f08014b
com.github.barteksc.pdfviewer.test:dimen/def_drawer_elevation = 0x7f06005d
com.github.barteksc.pdfviewer.test:dimen/compat_button_padding_vertical_material = 0x7f060059
com.github.barteksc.pdfviewer.test:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301aa
com.github.barteksc.pdfviewer.test:dimen/compat_button_inset_vertical_material = 0x7f060057
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301c6
com.github.barteksc.pdfviewer.test:dimen/compat_button_inset_horizontal_material = 0x7f060056
com.github.barteksc.pdfviewer.test:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c8
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderLayout = 0x7f0302b7
com.github.barteksc.pdfviewer.test:dimen/clock_face_margin_start = 0x7f060055
com.github.barteksc.pdfviewer.test:dimen/cardview_default_radius = 0x7f060054
com.github.barteksc.pdfviewer.test:dimen/cardview_default_elevation = 0x7f060053
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary40 = 0x7f05022f
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_subhead_material = 0x7f06004d
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_small_material = 0x7f06004c
com.github.barteksc.pdfviewer.test:id/sin = 0x7f080164
com.github.barteksc.pdfviewer.test:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602dc
com.github.barteksc.pdfviewer.test:styleable/CardView = 0x7f10001a
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_menu_material = 0x7f06004b
com.github.barteksc.pdfviewer.test:id/fixed = 0x7f0800a8
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.Dialog.Alert = 0x7f0f022a
com.github.barteksc.pdfviewer.test:integer/m3_badge_max_number = 0x7f090009
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_medium_material = 0x7f060049
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f060208
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_headline_material = 0x7f060047
com.github.barteksc.pdfviewer.test:string/mtrl_picker_navigate_to_current_year_description = 0x7f0e007b
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_display_4_material = 0x7f060046
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SearchView.Prefix = 0x7f0f03c0
com.github.barteksc.pdfviewer.test:integer/design_snackbar_text_max_lines = 0x7f090006
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_display_3_material = 0x7f060045
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f007d
com.github.barteksc.pdfviewer.test:attr/fontProviderQuery = 0x7f0301e7
com.github.barteksc.pdfviewer.test:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_display_2_material = 0x7f060044
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_button_material = 0x7f060041
com.github.barteksc.pdfviewer.test:dimen/abc_star_small = 0x7f06003d
com.github.barteksc.pdfviewer.test:drawable/design_ic_visibility_off = 0x7f070087
com.github.barteksc.pdfviewer.test:dimen/abc_star_medium = 0x7f06003c
com.github.barteksc.pdfviewer.test:dimen/abc_star_big = 0x7f06003b
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601f6
com.github.barteksc.pdfviewer.test:attr/homeAsUpIndicator = 0x7f030204
com.github.barteksc.pdfviewer.test:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.github.barteksc.pdfviewer.test:attr/buttonBarNeutralButtonStyle = 0x7f030085
com.github.barteksc.pdfviewer.test:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral94 = 0x7f050109
com.github.barteksc.pdfviewer.test:dimen/abc_search_view_preferred_height = 0x7f060036
com.github.barteksc.pdfviewer.test:string/error_a11y_label = 0x7f0e002e
com.github.barteksc.pdfviewer.test:dimen/abc_progress_bar_height_material = 0x7f060035
com.github.barteksc.pdfviewer.test:layout/abc_action_bar_title_item = 0x7f0b0000
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_bar_container_elevation = 0x7f06016d
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_19 = 0x7f08001b
com.github.barteksc.pdfviewer.test:id/stretch = 0x7f08017d
com.github.barteksc.pdfviewer.test:attr/drawableBottomCompat = 0x7f03016d
com.github.barteksc.pdfviewer.test:dimen/abc_panel_menu_list_width = 0x7f060034
com.github.barteksc.pdfviewer.test:color/m3_timepicker_button_text_color = 0x7f0501f3
com.github.barteksc.pdfviewer.test:dimen/abc_list_item_height_large_material = 0x7f060030
com.github.barteksc.pdfviewer.test:dimen/abc_floating_window_z = 0x7f06002f
com.github.barteksc.pdfviewer.test:integer/material_motion_duration_short_2 = 0x7f09002b
com.github.barteksc.pdfviewer.test:attr/startIconScaleType = 0x7f0303a6
com.github.barteksc.pdfviewer.test:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.github.barteksc.pdfviewer.test:color/mtrl_fab_ripple_color = 0x7f0502ae
com.github.barteksc.pdfviewer.test:attr/errorIconDrawable = 0x7f030196
com.github.barteksc.pdfviewer.test:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.github.barteksc.pdfviewer.test:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.github.barteksc.pdfviewer.test:attr/titlePositionInterpolator = 0x7f030442
com.github.barteksc.pdfviewer.test:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.github.barteksc.pdfviewer.test:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.github.barteksc.pdfviewer.test:attr/textColorAlertDialogListItem = 0x7f030411
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_padding_material = 0x7f060024
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar = 0x7f0f02ff
com.github.barteksc.pdfviewer.test:attr/maxImageSize = 0x7f0302dc
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_min_width_minor = 0x7f060023
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.github.barteksc.pdfviewer.test:id/actions = 0x7f080041
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary50 = 0x7f05012e
com.github.barteksc.pdfviewer.test:dimen/abc_control_padding_material = 0x7f06001a
com.github.barteksc.pdfviewer.test:styleable/MaterialTextView = 0x7f100058
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f02f2
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f0036
com.github.barteksc.pdfviewer.test:dimen/abc_control_corner_material = 0x7f060018
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Medium = 0x7f0f01a4
com.github.barteksc.pdfviewer.test:dimen/abc_button_padding_vertical_material = 0x7f060015
com.github.barteksc.pdfviewer.test:dimen/abc_button_inset_vertical_material = 0x7f060013
com.github.barteksc.pdfviewer.test:dimen/notification_main_column_padding_top = 0x7f06030a
com.github.barteksc.pdfviewer.test:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.github.barteksc.pdfviewer.test:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary20 = 0x7f05012b
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f0f039e
com.github.barteksc.pdfviewer.test:string/character_counter_content_description = 0x7f0e002a
com.github.barteksc.pdfviewer.test:id/with_icon = 0x7f0801bb
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface = 0x7f0501cc
com.github.barteksc.pdfviewer.test:attr/startIconContentDescription = 0x7f0303a3
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_elevation_material = 0x7f060005
com.github.barteksc.pdfviewer.test:style/Animation.AppCompat.DropDownUp = 0x7f0f0003
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.github.barteksc.pdfviewer.test:attr/windowFixedHeightMajor = 0x7f03047b
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.github.barteksc.pdfviewer.test:color/switch_thumb_normal_material_dark = 0x7f0502e3
com.github.barteksc.pdfviewer.test:id/autoComplete = 0x7f08004e
com.github.barteksc.pdfviewer.test:color/switch_thumb_material_dark = 0x7f0502e1
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f0f03d4
com.github.barteksc.pdfviewer.test:color/material_grey_600 = 0x7f050247
com.github.barteksc.pdfviewer.test:color/switch_thumb_disabled_material_light = 0x7f0502e0
com.github.barteksc.pdfviewer.test:color/switch_thumb_disabled_material_dark = 0x7f0502df
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a6
com.github.barteksc.pdfviewer.test:color/secondary_text_disabled_material_light = 0x7f0502de
com.github.barteksc.pdfviewer.test:attr/checkboxStyle = 0x7f0300a0
com.github.barteksc.pdfviewer.test:id/fitXY = 0x7f0800a7
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary100 = 0x7f0500c4
com.github.barteksc.pdfviewer.test:drawable/notification_action_background = 0x7f0700d8
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral4 = 0x7f0500ff
com.github.barteksc.pdfviewer.test:drawable/mtrl_popupmenu_background = 0x7f0700c8
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f06028a
com.github.barteksc.pdfviewer.test:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.github.barteksc.pdfviewer.test:color/secondary_text_disabled_material_dark = 0x7f0502dd
com.github.barteksc.pdfviewer.test:drawable/notification_tile_bg = 0x7f0700e2
com.github.barteksc.pdfviewer.test:color/secondary_text_default_material_light = 0x7f0502dc
com.github.barteksc.pdfviewer.test:id/checkbox = 0x7f080061
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Display4 = 0x7f0f019b
com.github.barteksc.pdfviewer.test:color/ripple_material_dark = 0x7f0502d9
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TonalButton.Icon = 0x7f0f035d
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary99 = 0x7f050229
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f06028e
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_14 = 0x7f080016
com.github.barteksc.pdfviewer.test:color/primary_text_default_material_light = 0x7f0502d6
com.github.barteksc.pdfviewer.test:dimen/material_helper_text_font_1_3_padding_top = 0x7f060237
com.github.barteksc.pdfviewer.test:color/primary_material_light = 0x7f0502d4
com.github.barteksc.pdfviewer.test:attr/background = 0x7f030043
com.github.barteksc.pdfviewer.test:attr/colorContainer = 0x7f0300de
com.github.barteksc.pdfviewer.test:color/primary_material_dark = 0x7f0502d3
com.github.barteksc.pdfviewer.test:color/primary_dark_material_light = 0x7f0502d2
com.github.barteksc.pdfviewer.test:color/primary_dark_material_dark = 0x7f0502d1
com.github.barteksc.pdfviewer.test:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0502ce
com.github.barteksc.pdfviewer.test:id/text_input_end_icon = 0x7f080195
com.github.barteksc.pdfviewer.test:id/multiply = 0x7f08010c
com.github.barteksc.pdfviewer.test:color/design_default_color_secondary = 0x7f050048
com.github.barteksc.pdfviewer.test:color/mtrl_textinput_focused_box_stroke_color = 0x7f0502cd
com.github.barteksc.pdfviewer.test:color/mtrl_textinput_disabled_color = 0x7f0502cb
com.github.barteksc.pdfviewer.test:dimen/m3_chip_hovered_translation_z = 0x7f0600f8
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Chip.Choice = 0x7f0f0402
com.github.barteksc.pdfviewer.test:color/mtrl_textinput_default_box_stroke_color = 0x7f0502ca
com.github.barteksc.pdfviewer.test:id/m3_side_sheet = 0x7f0800d2
com.github.barteksc.pdfviewer.test:id/forever = 0x7f0800ab
com.github.barteksc.pdfviewer.test:color/mtrl_text_btn_text_color_selector = 0x7f0502c9
com.github.barteksc.pdfviewer.test:attr/listPopupWindowStyle = 0x7f030298
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_surface = 0x7f050194
com.github.barteksc.pdfviewer.test:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060102
com.github.barteksc.pdfviewer.test:color/mtrl_tabs_ripple_color = 0x7f0502c8
com.github.barteksc.pdfviewer.test:color/mtrl_tabs_icon_color_selector_colored = 0x7f0502c6
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f0f0114
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f06018a
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_month_labeled = 0x7f0b004b
com.github.barteksc.pdfviewer.test:color/mtrl_tabs_icon_color_selector = 0x7f0502c5
com.github.barteksc.pdfviewer.test:attr/splitTrack = 0x7f03039e
com.github.barteksc.pdfviewer.test:color/mtrl_switch_track_decoration_tint = 0x7f0502c2
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
com.github.barteksc.pdfviewer.test:color/mtrl_switch_thumb_tint = 0x7f0502c1
com.github.barteksc.pdfviewer.test:color/mtrl_outlined_icon_tint = 0x7f0502bc
com.github.barteksc.pdfviewer.test:id/date_picker_actions = 0x7f080077
com.github.barteksc.pdfviewer.test:color/mtrl_on_surface_ripple_color = 0x7f0502bb
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_item_background_color = 0x7f0502b7
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_bar_item_tint = 0x7f0502b5
com.github.barteksc.pdfviewer.test:attr/colorOnTertiary = 0x7f0300f5
com.github.barteksc.pdfviewer.test:color/mtrl_indicator_text_color = 0x7f0502b2
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f0e003c
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f0601fb
com.github.barteksc.pdfviewer.test:layout/mtrl_auto_complete_simple_item = 0x7f0b0045
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary30 = 0x7f05023b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015f
com.github.barteksc.pdfviewer.test:color/mtrl_filled_icon_tint = 0x7f0502b0
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.TextView = 0x7f0f032a
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light = 0x7f0f023a
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Dark.Dialog = 0x7f0f005b
com.github.barteksc.pdfviewer.test:color/mtrl_filled_background_color = 0x7f0502af
com.github.barteksc.pdfviewer.test:attr/maxActionInlineWidth = 0x7f0302d8
com.github.barteksc.pdfviewer.test:color/mtrl_fab_icon_text_color_selector = 0x7f0502ad
com.github.barteksc.pdfviewer.test:color/mtrl_error = 0x7f0502ab
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f060247
com.github.barteksc.pdfviewer.test:color/mtrl_chip_close_icon_tint = 0x7f0502a5
com.github.barteksc.pdfviewer.test:color/mtrl_card_view_ripple = 0x7f0502a3
com.github.barteksc.pdfviewer.test:styleable/AppCompatEmojiHelper = 0x7f10000d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ListView = 0x7f0f0317
com.github.barteksc.pdfviewer.test:color/mtrl_calendar_selected_range = 0x7f0502a1
com.github.barteksc.pdfviewer.test:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.github.barteksc.pdfviewer.test:color/mtrl_btn_text_color_selector = 0x7f05029e
com.github.barteksc.pdfviewer.test:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.github.barteksc.pdfviewer.test:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.github.barteksc.pdfviewer.test:color/mtrl_btn_text_btn_ripple_color = 0x7f05029c
com.github.barteksc.pdfviewer.test:id/up = 0x7f0801b1
com.github.barteksc.pdfviewer.test:color/mtrl_btn_ripple_color = 0x7f050299
com.github.barteksc.pdfviewer.test:color/material_timepicker_clockface = 0x7f050296
com.github.barteksc.pdfviewer.test:color/material_timepicker_clock_text_color = 0x7f050295
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f0f0073
com.github.barteksc.pdfviewer.test:attr/layout_constraintVertical_weight = 0x7f030276
com.github.barteksc.pdfviewer.test:color/material_timepicker_button_background = 0x7f050293
com.github.barteksc.pdfviewer.test:color/material_slider_inactive_track_color = 0x7f050291
com.github.barteksc.pdfviewer.test:layout/abc_activity_chooser_view = 0x7f0b0006
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_8 = 0x7f08002e
com.github.barteksc.pdfviewer.test:styleable/ColorStateListItem = 0x7f100023
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_error = 0x7f050262
com.github.barteksc.pdfviewer.test:color/material_slider_inactive_tick_marks_color = 0x7f050290
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_day_height = 0x7f060270
com.github.barteksc.pdfviewer.test:drawable/abc_textfield_default_mtrl_alpha = 0x7f070072
com.github.barteksc.pdfviewer.test:color/material_slider_active_track_color = 0x7f05028e
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.TextView = 0x7f0f00f8
com.github.barteksc.pdfviewer.test:color/material_personalized_hint_foreground_inverse = 0x7f05028a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f060160
com.github.barteksc.pdfviewer.test:color/material_personalized_hint_foreground = 0x7f050289
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501b1
com.github.barteksc.pdfviewer.test:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f050288
com.github.barteksc.pdfviewer.test:style/Theme.Design = 0x7f0f0221
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_inset_small = 0x7f0602d0
com.github.barteksc.pdfviewer.test:attr/toolbarId = 0x7f030448
com.github.barteksc.pdfviewer.test:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f050287
com.github.barteksc.pdfviewer.test:dimen/m3_btn_padding_right = 0x7f0600db
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500a7
com.github.barteksc.pdfviewer.test:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f050286
com.github.barteksc.pdfviewer.test:color/material_personalized_color_text_primary_inverse = 0x7f050285
com.github.barteksc.pdfviewer.test:color/material_personalized_color_text_hint_foreground_inverse = 0x7f050284
com.github.barteksc.pdfviewer.test:color/material_personalized_color_tertiary_container = 0x7f050283
com.github.barteksc.pdfviewer.test:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0162
com.github.barteksc.pdfviewer.test:color/material_personalized_color_tertiary = 0x7f050282
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006c
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_dim = 0x7f05027f
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.MaterialComponents.Light = 0x7f0f00a7
com.github.barteksc.pdfviewer.test:attr/flow_firstHorizontalStyle = 0x7f0301ce
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f060263
com.github.barteksc.pdfviewer.test:color/material_personalized_color_primary = 0x7f05026f
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_container_high = 0x7f05027b
com.github.barteksc.pdfviewer.test:layout/mtrl_layout_snackbar = 0x7f0b0050
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_container = 0x7f05027a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Slider = 0x7f0f03c6
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Chip = 0x7f0f01fc
com.github.barteksc.pdfviewer.test:attr/chipStrokeWidth = 0x7f0300bd
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060127
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f0f010b
com.github.barteksc.pdfviewer.test:attr/materialCalendarYearNavigationButton = 0x7f0302bf
com.github.barteksc.pdfviewer.test:color/material_personalized_color_secondary = 0x7f050274
com.github.barteksc.pdfviewer.test:attr/buttonBarNegativeButtonStyle = 0x7f030084
com.github.barteksc.pdfviewer.test:attr/layout_anchorGravity = 0x7f03024d
com.github.barteksc.pdfviewer.test:color/material_personalized_color_primary_text_inverse = 0x7f050273
com.github.barteksc.pdfviewer.test:attr/tabPaddingTop = 0x7f0303de
com.github.barteksc.pdfviewer.test:color/material_personalized_color_primary_container = 0x7f050270
com.github.barteksc.pdfviewer.test:dimen/mtrl_toolbar_default_height = 0x7f0602fd
com.github.barteksc.pdfviewer.test:id/parallax = 0x7f08012e
com.github.barteksc.pdfviewer.test:color/material_personalized_color_outline_variant = 0x7f05026e
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_tertiary = 0x7f05026b
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f01aa
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_surface_inverse = 0x7f050269
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f0025
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_secondary_container = 0x7f050267
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_error_container = 0x7f050263
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f0f0133
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050181
com.github.barteksc.pdfviewer.test:drawable/$avd_show_password__2 = 0x7f070005
com.github.barteksc.pdfviewer.test:color/material_personalized_color_error = 0x7f05025f
com.github.barteksc.pdfviewer.test:color/material_personalized_color_control_normal = 0x7f05025e
com.github.barteksc.pdfviewer.test:attr/alphabeticModifiers = 0x7f03002d
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_end_padding = 0x7f0602a3
com.github.barteksc.pdfviewer.test:color/error_color_material_dark = 0x7f050059
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_item_min_width = 0x7f060066
com.github.barteksc.pdfviewer.test:color/material_personalized_color_control_activated = 0x7f05025c
com.github.barteksc.pdfviewer.test:attr/touchRegionId = 0x7f030453
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button = 0x7f0700b3
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f0f041a
com.github.barteksc.pdfviewer.test:color/material_on_surface_emphasis_medium = 0x7f050257
com.github.barteksc.pdfviewer.test:color/material_on_primary_emphasis_high_type = 0x7f050253
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f0f02c5
com.github.barteksc.pdfviewer.test:color/material_on_background_emphasis_high_type = 0x7f050250
com.github.barteksc.pdfviewer.test:color/m3_chip_ripple_color = 0x7f050071
com.github.barteksc.pdfviewer.test:color/material_on_background_disabled = 0x7f05024f
com.github.barteksc.pdfviewer.test:color/material_harmonized_color_on_error_container = 0x7f05024e
com.github.barteksc.pdfviewer.test:color/material_harmonized_color_on_error = 0x7f05024d
com.github.barteksc.pdfviewer.test:attr/boxBackgroundMode = 0x7f030078
com.github.barteksc.pdfviewer.test:drawable/abc_ic_ab_back_material = 0x7f07003d
com.github.barteksc.pdfviewer.test:dimen/mtrl_shape_corner_size_large_component = 0x7f0602db
com.github.barteksc.pdfviewer.test:dimen/material_emphasis_disabled = 0x7f06022b
com.github.barteksc.pdfviewer.test:color/material_harmonized_color_error = 0x7f05024b
com.github.barteksc.pdfviewer.test:style/Widget.Material3.SearchView.Toolbar = 0x7f0f03c1
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00af
com.github.barteksc.pdfviewer.test:dimen/abc_action_button_min_height_material = 0x7f06000d
com.github.barteksc.pdfviewer.test:color/material_grey_900 = 0x7f05024a
com.github.barteksc.pdfviewer.test:drawable/abc_btn_borderless_material = 0x7f07002a
com.github.barteksc.pdfviewer.test:color/material_grey_850 = 0x7f050249
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500b8
com.github.barteksc.pdfviewer.test:color/material_grey_100 = 0x7f050244
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary95 = 0x7f050242
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500ba
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary90 = 0x7f050241
com.github.barteksc.pdfviewer.test:id/SYM = 0x7f08000b
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Prefix = 0x7f0f01cf
com.github.barteksc.pdfviewer.test:id/tag_accessibility_heading = 0x7f080183
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary20 = 0x7f05023a
com.github.barteksc.pdfviewer.test:attr/textColorSearchUrl = 0x7f030412
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary10 = 0x7f050238
com.github.barteksc.pdfviewer.test:attr/collapsedSize = 0x7f0300d1
com.github.barteksc.pdfviewer.test:id/dragRight = 0x7f08008c
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary0 = 0x7f050237
com.github.barteksc.pdfviewer.test:id/cradle = 0x7f080073
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f060207
com.github.barteksc.pdfviewer.test:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f013d
com.github.barteksc.pdfviewer.test:attr/indicatorColor = 0x7f030215
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary99 = 0x7f050236
com.github.barteksc.pdfviewer.test:attr/bottomSheetStyle = 0x7f030076
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary90 = 0x7f050234
com.github.barteksc.pdfviewer.test:id/open_search_view_scrim = 0x7f080126
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary70 = 0x7f050232
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary30 = 0x7f05022e
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary20 = 0x7f05022d
com.github.barteksc.pdfviewer.test:dimen/abc_control_inset_material = 0x7f060019
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary10 = 0x7f05022b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_circular_progress_indicator_active_indicator_width = 0x7f060104
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary0 = 0x7f05022a
com.github.barteksc.pdfviewer.test:styleable/SwitchCompat = 0x7f100081
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary95 = 0x7f050228
com.github.barteksc.pdfviewer.test:styleable/MaterialAlertDialogTheme = 0x7f10004a
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary90 = 0x7f050227
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary80 = 0x7f050226
com.github.barteksc.pdfviewer.test:dimen/material_cursor_width = 0x7f060229
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary60 = 0x7f050224
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary30 = 0x7f050221
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant99 = 0x7f05021c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f0f03b8
com.github.barteksc.pdfviewer.test:attr/layout_scrollFlags = 0x7f030288
com.github.barteksc.pdfviewer.test:color/m3_chip_assist_text_color = 0x7f05006f
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant70 = 0x7f050218
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant60 = 0x7f050217
com.github.barteksc.pdfviewer.test:style/Animation.Material3.SideSheetDialog.Left = 0x7f0f0008
com.github.barteksc.pdfviewer.test:dimen/m3_btn_icon_only_default_padding = 0x7f0600d3
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant50 = 0x7f050216
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant40 = 0x7f050215
com.github.barteksc.pdfviewer.test:drawable/abc_ratingbar_indicator_material = 0x7f07005a
com.github.barteksc.pdfviewer.test:attr/expandedTitleMarginEnd = 0x7f0301a2
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c4
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant20 = 0x7f050213
com.github.barteksc.pdfviewer.test:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_min_height = 0x7f0602a7
com.github.barteksc.pdfviewer.test:attr/values = 0x7f03046c
com.github.barteksc.pdfviewer.test:attr/iconSize = 0x7f03020d
com.github.barteksc.pdfviewer.test:dimen/design_bottom_sheet_elevation = 0x7f06006b
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral95 = 0x7f05020e
com.github.barteksc.pdfviewer.test:dimen/mtrl_tooltip_cornerSize = 0x7f0602ff
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral90 = 0x7f05020d
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral80 = 0x7f05020c
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral70 = 0x7f05020b
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral60 = 0x7f05020a
com.github.barteksc.pdfviewer.test:attr/buttonTintMode = 0x7f030092
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_secondary_container = 0x7f0501cb
com.github.barteksc.pdfviewer.test:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601b1
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral100 = 0x7f050205
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f0f0119
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_margin_vertical = 0x7f0601d5
com.github.barteksc.pdfviewer.test:attr/deltaPolarAngle = 0x7f03015a
com.github.barteksc.pdfviewer.test:color/material_deep_teal_500 = 0x7f050201
com.github.barteksc.pdfviewer.test:attr/checkedIconTint = 0x7f0300a8
com.github.barteksc.pdfviewer.test:color/material_deep_teal_200 = 0x7f050200
com.github.barteksc.pdfviewer.test:color/material_cursor_color = 0x7f0501ff
com.github.barteksc.pdfviewer.test:color/material_blue_grey_900 = 0x7f0501fd
com.github.barteksc.pdfviewer.test:style/Widget.Material3.NavigationView = 0x7f0f03b6
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0164
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009d
com.github.barteksc.pdfviewer.test:id/asConfigured = 0x7f08004b
com.github.barteksc.pdfviewer.test:color/m3_tonal_button_ripple_color_selector = 0x7f0501fb
com.github.barteksc.pdfviewer.test:id/contentPanel = 0x7f08006e
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f01a2
com.github.barteksc.pdfviewer.test:attr/popupTheme = 0x7f03034a
com.github.barteksc.pdfviewer.test:color/m3_timepicker_secondary_text_button_text_color = 0x7f0501f9
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_height_fullscreen = 0x7f06027b
com.github.barteksc.pdfviewer.test:color/m3_timepicker_display_text_color = 0x7f0501f7
com.github.barteksc.pdfviewer.test:string/abc_shareactionprovider_share_with = 0x7f0e0018
com.github.barteksc.pdfviewer.test:color/m3_timepicker_clock_text_color = 0x7f0501f4
com.github.barteksc.pdfviewer.test:color/m3_timepicker_button_background_color = 0x7f0501f1
com.github.barteksc.pdfviewer.test:attr/targetId = 0x7f0303e7
com.github.barteksc.pdfviewer.test:color/m3_textfield_label_color = 0x7f0501ef
com.github.barteksc.pdfviewer.test:drawable/notification_bg_low_normal = 0x7f0700db
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant95 = 0x7f05021b
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ImageButton = 0x7f0f02fe
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
com.github.barteksc.pdfviewer.test:color/m3_textfield_input_text_color = 0x7f0501ee
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c009a
com.github.barteksc.pdfviewer.test:color/m3_textfield_indicator_text_color = 0x7f0501ed
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Toolbar = 0x7f0f032c
com.github.barteksc.pdfviewer.test:color/m3_textfield_filled_background_color = 0x7f0501ec
com.github.barteksc.pdfviewer.test:color/m3_text_button_foreground_color_selector = 0x7f0501ea
com.github.barteksc.pdfviewer.test:color/m3_tabs_text_color_secondary = 0x7f0501e8
com.github.barteksc.pdfviewer.test:dimen/m3_btn_padding_top = 0x7f0600dc
com.github.barteksc.pdfviewer.test:color/m3_tabs_text_color = 0x7f0501e7
com.github.barteksc.pdfviewer.test:id/open_search_view_root = 0x7f080125
com.github.barteksc.pdfviewer.test:dimen/m3_btn_padding_bottom = 0x7f0600d9
com.github.barteksc.pdfviewer.test:color/m3_tabs_ripple_color = 0x7f0501e5
com.github.barteksc.pdfviewer.test:string/mtrl_picker_invalid_format_example = 0x7f0e0078
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f060178
com.github.barteksc.pdfviewer.test:color/m3_sys_color_secondary_fixed_dim = 0x7f0501e0
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f06018f
com.github.barteksc.pdfviewer.test:attr/allowStacking = 0x7f03002b
com.github.barteksc.pdfviewer.test:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0501dc
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0102
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060149
com.github.barteksc.pdfviewer.test:color/mtrl_btn_stroke_color_selector = 0x7f05029a
com.github.barteksc.pdfviewer.test:attr/tickMarkTint = 0x7f030430
com.github.barteksc.pdfviewer.test:color/m3_sys_color_on_tertiary_fixed = 0x7f0501db
com.github.barteksc.pdfviewer.test:attr/overlay = 0x7f03032a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_on_primary_fixed = 0x7f0501d7
com.github.barteksc.pdfviewer.test:string/mtrl_picker_today_description = 0x7f0e008b
com.github.barteksc.pdfviewer.test:color/design_default_color_primary_variant = 0x7f050047
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_tertiary_container = 0x7f0501d6
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f060205
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_tertiary = 0x7f0501d5
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.NoActionBar = 0x7f0f0220
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_dim = 0x7f0501d3
com.github.barteksc.pdfviewer.test:drawable/material_ic_calendar_black_24dp = 0x7f0700a9
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060139
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_text_input_date = 0x7f0b005b
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_container_high = 0x7f0501cf
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_primary_container = 0x7f0501c9
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_outline_variant = 0x7f0501c7
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_outline = 0x7f0501c6
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.Small = 0x7f0f0174
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_tertiary = 0x7f0501c4
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f060278
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_surface_variant = 0x7f0501c3
com.github.barteksc.pdfviewer.test:styleable/FlowLayout = 0x7f100032
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_surface = 0x7f0501c2
com.github.barteksc.pdfviewer.test:color/material_personalized__highlighted_text_inverse = 0x7f05025a
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_title_divider_material = 0x7f060026
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_secondary_container = 0x7f0501c1
com.github.barteksc.pdfviewer.test:attr/editTextStyle = 0x7f030180
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_secondary = 0x7f0501c0
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_primary_container = 0x7f0501bf
com.github.barteksc.pdfviewer.test:dimen/m3_comp_badge_large_size = 0x7f0600ff
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_inverse_surface = 0x7f0501ba
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomSheet.Modal = 0x7f0f034b
com.github.barteksc.pdfviewer.test:string/bottom_sheet_behavior = 0x7f0e001d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f060185
com.github.barteksc.pdfviewer.test:dimen/m3_badge_offset = 0x7f0600b4
com.github.barteksc.pdfviewer.test:string/bottomsheet_drag_handle_clicked = 0x7f0e0021
com.github.barteksc.pdfviewer.test:dimen/compat_button_padding_horizontal_material = 0x7f060058
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_inverse_primary = 0x7f0501b9
com.github.barteksc.pdfviewer.test:styleable/MotionHelper = 0x7f100060
com.github.barteksc.pdfviewer.test:attr/trackColorActive = 0x7f030456
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_inverse_on_surface = 0x7f0501b8
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_error = 0x7f0501b6
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_inset = 0x7f06025b
com.github.barteksc.pdfviewer.test:id/uniform = 0x7f0801af
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014d
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_background = 0x7f0501b5
com.github.barteksc.pdfviewer.test:drawable/btn_radio_on_mtrl = 0x7f07007f
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral99 = 0x7f05020f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501b3
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501af
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f0f03ec
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f02e8
com.github.barteksc.pdfviewer.test:drawable/avd_hide_password = 0x7f070077
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501ae
com.github.barteksc.pdfviewer.test:integer/m3_sys_shape_corner_large_corner_family = 0x7f090023
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501ad
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Tooltip = 0x7f0f045b
com.github.barteksc.pdfviewer.test:string/mtrl_picker_toggle_to_year_selection = 0x7f0e008f
com.github.barteksc.pdfviewer.test:attr/applyMotionScene = 0x7f030034
com.github.barteksc.pdfviewer.test:drawable/m3_bottom_sheet_drag_handle = 0x7f07009f
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f002c
com.github.barteksc.pdfviewer.test:color/m3_chip_text_color = 0x7f050073
com.github.barteksc.pdfviewer.test:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070060
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.Chip = 0x7f0f0101
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501ac
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501aa
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501a9
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501a8
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501a6
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501a5
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501a1
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501a0
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_1 = 0x7f080011
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009f
com.github.barteksc.pdfviewer.test:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601a3
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_bright = 0x7f05019f
com.github.barteksc.pdfviewer.test:drawable/ic_clock_black_24dp = 0x7f070092
com.github.barteksc.pdfviewer.test:attr/simpleItems = 0x7f030391
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface = 0x7f05019e
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f00a2
com.github.barteksc.pdfviewer.test:color/foreground_material_dark = 0x7f05005b
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_secondary = 0x7f05019c
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral40 = 0x7f050208
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_primary_container = 0x7f05019b
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f0f03a7
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_outline = 0x7f050198
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f050197
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f01b4
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602cb
com.github.barteksc.pdfviewer.test:style/Animation.Material3.SideSheetDialog = 0x7f0f0007
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f050195
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f050191
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f05018e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f05018d
com.github.barteksc.pdfviewer.test:color/abc_search_url_text_pressed = 0x7f05000f
com.github.barteksc.pdfviewer.test:color/m3_dynamic_dark_hint_foreground = 0x7f05007e
com.github.barteksc.pdfviewer.test:color/mtrl_textinput_filled_box_default_background_color = 0x7f0502cc
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f031e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f05018c
com.github.barteksc.pdfviewer.test:styleable/PopupWindow = 0x7f10006a
com.github.barteksc.pdfviewer.test:attr/textStartPadding = 0x7f03041d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0600fd
com.github.barteksc.pdfviewer.test:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f0f00b2
com.github.barteksc.pdfviewer.test:string/mtrl_picker_day_of_week_column_header = 0x7f0e0075
com.github.barteksc.pdfviewer.test:id/open_search_view_header_container = 0x7f080124
com.github.barteksc.pdfviewer.test:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f0501f8
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceCornerExtraLarge = 0x7f030377
com.github.barteksc.pdfviewer.test:color/abc_tint_switch_track = 0x7f050018
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_tertiary = 0x7f050189
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602a1
com.github.barteksc.pdfviewer.test:id/selected = 0x7f08015e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f050188
com.github.barteksc.pdfviewer.test:styleable/Motion = 0x7f10005f
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f0f015e
com.github.barteksc.pdfviewer.test:layout/m3_alert_dialog = 0x7f0b002c
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary99 = 0x7f050243
com.github.barteksc.pdfviewer.test:string/material_slider_range_start = 0x7f0e0053
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f050186
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500cf
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f050185
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionMode = 0x7f0f00ca
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f050184
com.github.barteksc.pdfviewer.test:drawable/abc_star_black_48dp = 0x7f070067
com.github.barteksc.pdfviewer.test:dimen/m3_side_sheet_width = 0x7f0601e0
com.github.barteksc.pdfviewer.test:attr/scrimBackground = 0x7f03036b
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f050183
com.github.barteksc.pdfviewer.test:styleable/ViewPager2 = 0x7f100090
com.github.barteksc.pdfviewer.test:color/m3_button_ripple_color = 0x7f050066
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050182
com.github.barteksc.pdfviewer.test:dimen/hint_alpha_material_light = 0x7f060097
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f05017f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_secondary = 0x7f05017e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_primary_container = 0x7f05017d
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_primary = 0x7f05017c
com.github.barteksc.pdfviewer.test:attr/contentInsetRight = 0x7f030123
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f05017b
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_outline = 0x7f05017a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f050179
com.github.barteksc.pdfviewer.test:color/notification_icon_bg_color = 0x7f0502d0
com.github.barteksc.pdfviewer.test:color/m3_selection_control_ripple_color_selector = 0x7f050143
com.github.barteksc.pdfviewer.test:attr/maxCharacterCount = 0x7f0302da
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_tertiary = 0x7f05015a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f050178
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.MaterialComponents = 0x7f0f00a5
com.github.barteksc.pdfviewer.test:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f050177
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_surface = 0x7f050176
com.github.barteksc.pdfviewer.test:id/motion_base = 0x7f0800f4
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050174
com.github.barteksc.pdfviewer.test:attr/contentInsetLeft = 0x7f030122
com.github.barteksc.pdfviewer.test:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bb
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050173
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050172
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_background = 0x7f050171
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextView = 0x7f0f044c
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Button = 0x7f0f00cd
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600af
com.github.barteksc.pdfviewer.test:integer/material_motion_duration_medium_1 = 0x7f090028
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a4
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f0f02ca
com.github.barteksc.pdfviewer.test:drawable/$avd_show_password__0 = 0x7f070003
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f05016f
com.github.barteksc.pdfviewer.test:attr/colorSecondaryFixed = 0x7f030105
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f05016e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f0f0381
com.github.barteksc.pdfviewer.test:drawable/abc_seekbar_thumb_material = 0x7f070062
com.github.barteksc.pdfviewer.test:integer/mtrl_calendar_selection_text_lines = 0x7f090031
com.github.barteksc.pdfviewer.test:attr/dropDownListViewStyle = 0x7f03017a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_background = 0x7f05016d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ListMenuView = 0x7f0f0315
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_tertiary_container = 0x7f05016c
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016f
com.github.barteksc.pdfviewer.test:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
com.github.barteksc.pdfviewer.test:color/material_slider_thumb_color = 0x7f050292
com.github.barteksc.pdfviewer.test:dimen/m3_searchview_elevation = 0x7f0601db
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_dim = 0x7f050169
com.github.barteksc.pdfviewer.test:color/material_slider_halo_color = 0x7f05028f
com.github.barteksc.pdfviewer.test:attr/lineSpacing = 0x7f03028f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_container_low = 0x7f050167
com.github.barteksc.pdfviewer.test:attr/dividerInsetStart = 0x7f030165
com.github.barteksc.pdfviewer.test:attr/behavior_autoShrink = 0x7f030064
com.github.barteksc.pdfviewer.test:attr/errorIconTint = 0x7f030197
com.github.barteksc.pdfviewer.test:dimen/material_clock_face_margin_top = 0x7f06021e
com.github.barteksc.pdfviewer.test:dimen/m3_toolbar_text_size_title = 0x7f060219
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f0f03d9
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f0f0181
com.github.barteksc.pdfviewer.test:attr/checkedChip = 0x7f0300a2
com.github.barteksc.pdfviewer.test:color/design_fab_shadow_end_color = 0x7f05004c
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_container_high = 0x7f050165
com.github.barteksc.pdfviewer.test:attr/thumbIconTintMode = 0x7f030425
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_tertiary_container = 0x7f05015b
com.github.barteksc.pdfviewer.test:string/material_timepicker_select_time = 0x7f0e005a
com.github.barteksc.pdfviewer.test:attr/buttonPanelSideLayout = 0x7f03008e
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500dd
com.github.barteksc.pdfviewer.test:attr/defaultDuration = 0x7f030155
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_secondary_container = 0x7f050157
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_secondary = 0x7f050156
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_inverse_primary = 0x7f05014f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_inverse_on_surface = 0x7f05014e
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0155
com.github.barteksc.pdfviewer.test:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0108
com.github.barteksc.pdfviewer.test:layout/design_menu_item_action_area = 0x7f0b0023
com.github.barteksc.pdfviewer.test:color/primary_text_disabled_material_dark = 0x7f0502d7
com.github.barteksc.pdfviewer.test:attr/actionModePopupWindowStyle = 0x7f030018
com.github.barteksc.pdfviewer.test:attr/textInputFilledDenseStyle = 0x7f030414
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_error_container = 0x7f05014d
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f0017
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary70 = 0x7f050225
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.github.barteksc.pdfviewer.test:style/Widget.Design.TextInputLayout = 0x7f0f033a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500e8
com.github.barteksc.pdfviewer.test:color/m3_slider_thumb_color = 0x7f050148
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f00d6
com.github.barteksc.pdfviewer.test:color/m3_slider_halo_color = 0x7f050146
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary99 = 0x7f050141
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary90 = 0x7f05013f
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f0f029f
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Caption = 0x7f0f0197
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary99 = 0x7f050134
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceCornerSmall = 0x7f03037b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary40 = 0x7f05013a
com.github.barteksc.pdfviewer.test:layout/mtrl_alert_dialog_title = 0x7f0b0041
com.github.barteksc.pdfviewer.test:attr/defaultMarginsEnabled = 0x7f030156
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary30 = 0x7f050139
com.github.barteksc.pdfviewer.test:attr/actionModeWebSearchDrawable = 0x7f03001e
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Light = 0x7f0f0095
com.github.barteksc.pdfviewer.test:attr/elevationOverlayColor = 0x7f030183
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c0071
com.github.barteksc.pdfviewer.test:attr/popupWindowStyle = 0x7f03034b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary95 = 0x7f050133
com.github.barteksc.pdfviewer.test:styleable/CircularProgressIndicator = 0x7f10001e
com.github.barteksc.pdfviewer.test:attr/dialogCornerRadius = 0x7f03015d
com.github.barteksc.pdfviewer.test:id/circle_center = 0x7f080064
com.github.barteksc.pdfviewer.test:attr/maxVelocity = 0x7f0302df
com.github.barteksc.pdfviewer.test:attr/navigationContentDescription = 0x7f030317
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary70 = 0x7f050130
com.github.barteksc.pdfviewer.test:attr/titleMarginBottom = 0x7f03043d
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary60 = 0x7f05012f
com.github.barteksc.pdfviewer.test:attr/emojiCompatEnabled = 0x7f030185
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary99 = 0x7f050127
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary80 = 0x7f050124
com.github.barteksc.pdfviewer.test:attr/textAppearanceLargePopupMenu = 0x7f030402
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary50 = 0x7f050121
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f0f03f8
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Input = 0x7f0f0367
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016b
com.github.barteksc.pdfviewer.test:color/m3_icon_button_icon_color_selector = 0x7f05008d
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f0f0134
com.github.barteksc.pdfviewer.test:drawable/ic_mtrl_chip_close_circle = 0x7f07009a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary100 = 0x7f05011d
com.github.barteksc.pdfviewer.test:attr/path_percent = 0x7f03033d
com.github.barteksc.pdfviewer.test:color/m3_timepicker_display_ripple_color = 0x7f0501f6
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary0 = 0x7f05011b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant50 = 0x7f050114
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant40 = 0x7f050113
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Slider.Label = 0x7f0f03c7
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_secondary = 0x7f050266
com.github.barteksc.pdfviewer.test:attr/sb_horizontal = 0x7f030367
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral92 = 0x7f050108
com.github.barteksc.pdfviewer.test:attr/reverseLayout = 0x7f030361
com.github.barteksc.pdfviewer.test:attr/backgroundSplit = 0x7f03004a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral87 = 0x7f050106
com.github.barteksc.pdfviewer.test:attr/defaultScrollFlagsEnabled = 0x7f030158
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral30 = 0x7f0500fe
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral22 = 0x7f0500fc
com.github.barteksc.pdfviewer.test:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f013e
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f0f0104
com.github.barteksc.pdfviewer.test:dimen/material_clock_period_toggle_width = 0x7f060226
com.github.barteksc.pdfviewer.test:color/mtrl_filled_stroke_color = 0x7f0502b1
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_tertiary_container = 0x7f0501c5
com.github.barteksc.pdfviewer.test:attr/expandedTitleTextColor = 0x7f0301a6
com.github.barteksc.pdfviewer.test:color/design_fab_stroke_top_inner_color = 0x7f050051
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500b7
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f0f042f
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500a0
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f0f0383
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral10 = 0x7f0500f7
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral0 = 0x7f0500f6
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0126
com.github.barteksc.pdfviewer.test:layout/m3_alert_dialog_actions = 0x7f0b002d
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014e
com.github.barteksc.pdfviewer.test:attr/removeEmbeddedFabElevation = 0x7f030360
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error95 = 0x7f0500f4
com.github.barteksc.pdfviewer.test:attr/paddingBottomNoButtons = 0x7f03032b
com.github.barteksc.pdfviewer.test:animator/fragment_fade_enter = 0x7f020005
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f00e8
com.github.barteksc.pdfviewer.test:attr/simpleItemSelectedRippleColor = 0x7f030390
com.github.barteksc.pdfviewer.test:attr/minSeparation = 0x7f0302e7
com.github.barteksc.pdfviewer.test:color/material_blue_grey_950 = 0x7f0501fe
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error90 = 0x7f0500f3
com.github.barteksc.pdfviewer.test:id/message = 0x7f0800eb
com.github.barteksc.pdfviewer.test:attr/tabRippleColor = 0x7f0303df
com.github.barteksc.pdfviewer.test:drawable/material_ic_edit_black_24dp = 0x7f0700ab
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f0f006f
com.github.barteksc.pdfviewer.test:attr/flow_lastHorizontalStyle = 0x7f0301d6
com.github.barteksc.pdfviewer.test:attr/cardForegroundColor = 0x7f030096
com.github.barteksc.pdfviewer.test:color/design_fab_shadow_start_color = 0x7f05004e
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error80 = 0x7f0500f2
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f00d4
com.github.barteksc.pdfviewer.test:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f060233
com.github.barteksc.pdfviewer.test:attr/prefixTextAppearance = 0x7f03034d
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f0f0292
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500df
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant80 = 0x7f050117
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0053
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500de
com.github.barteksc.pdfviewer.test:styleable/MaterialCardView = 0x7f100050
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f050196
com.github.barteksc.pdfviewer.test:drawable/ic_search_black_24 = 0x7f07009b
com.github.barteksc.pdfviewer.test:dimen/material_clock_hand_center_dot_radius = 0x7f06021f
com.github.barteksc.pdfviewer.test:id/mtrl_calendar_day_selector_frame = 0x7f0800f6
com.github.barteksc.pdfviewer.test:attr/layout_collapseParallaxMultiplier = 0x7f030250
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Small = 0x7f0f01a9
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500da
com.github.barteksc.pdfviewer.test:color/mtrl_choice_chip_ripple_color = 0x7f0502a9
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500d5
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSurfaceStyle = 0x7f0301cb
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0021
com.github.barteksc.pdfviewer.test:color/dim_foreground_material_dark = 0x7f050057
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500d4
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500d0
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
com.github.barteksc.pdfviewer.test:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301a9
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary99 = 0x7f0500ce
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary80 = 0x7f0500cb
com.github.barteksc.pdfviewer.test:attr/errorIconTintMode = 0x7f030198
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary40 = 0x7f0500c7
com.github.barteksc.pdfviewer.test:attr/actionModePasteDrawable = 0x7f030017
com.github.barteksc.pdfviewer.test:attr/backgroundOverlayColorAlpha = 0x7f030049
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary90 = 0x7f050132
com.github.barteksc.pdfviewer.test:attr/listPreferredItemPaddingLeft = 0x7f03029d
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f0f0160
com.github.barteksc.pdfviewer.test:attr/buttonIconDimen = 0x7f03008b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500bf
com.github.barteksc.pdfviewer.test:styleable/TextInputLayout = 0x7f100087
com.github.barteksc.pdfviewer.test:id/staticPostLayout = 0x7f08017b
com.github.barteksc.pdfviewer.test:attr/liftOnScrollColor = 0x7f03028b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500bd
com.github.barteksc.pdfviewer.test:string/mtrl_switch_thumb_path_unchecked = 0x7f0e0095
com.github.barteksc.pdfviewer.test:attr/visibilityMode = 0x7f030470
com.github.barteksc.pdfviewer.test:color/bright_foreground_disabled_material_dark = 0x7f050021
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f0f0417
com.github.barteksc.pdfviewer.test:attr/colorOutlineVariant = 0x7f0300fa
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500b4
com.github.barteksc.pdfviewer.test:attr/customDimension = 0x7f03014b
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.Snackbar = 0x7f0f010d
com.github.barteksc.pdfviewer.test:attr/tickRadiusInactive = 0x7f030433
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500b1
com.github.barteksc.pdfviewer.test:attr/state_with_icon = 0x7f0303b1
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f0f0188
com.github.barteksc.pdfviewer.test:id/decor_content_parent = 0x7f08007a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500ae
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500a9
com.github.barteksc.pdfviewer.test:string/fab_transformation_sheet_behavior = 0x7f0e0032
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500a1
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral98 = 0x7f05010c
com.github.barteksc.pdfviewer.test:attr/windowActionModeOverlay = 0x7f03047a
com.github.barteksc.pdfviewer.test:color/m3_primary_text_disable_only = 0x7f050099
com.github.barteksc.pdfviewer.test:id/list_item = 0x7f0800d1
com.github.barteksc.pdfviewer.test:attr/bottomAppBarStyle = 0x7f030071
com.github.barteksc.pdfviewer.test:layout/design_bottom_sheet_dialog = 0x7f0b001e
com.github.barteksc.pdfviewer.test:color/m3_popupmenu_overlay_color = 0x7f050098
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_item_icon_tint = 0x7f0502b8
com.github.barteksc.pdfviewer.test:layout/select_dialog_singlechoice_material = 0x7f0b0067
com.github.barteksc.pdfviewer.test:color/m3_navigation_rail_ripple_color_selector = 0x7f050097
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_primary = 0x7f0501be
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TabLayout.OnSurface = 0x7f0f03cc
com.github.barteksc.pdfviewer.test:attr/clockFaceBackgroundColor = 0x7f0300c3
com.github.barteksc.pdfviewer.test:color/m3_navigation_item_background_color = 0x7f050091
com.github.barteksc.pdfviewer.test:color/m3_navigation_bar_ripple_color_selector = 0x7f050090
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f0151
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral0 = 0x7f05009d
com.github.barteksc.pdfviewer.test:id/homeAsUp = 0x7f0800b8
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_primary_variant = 0x7f05003a
com.github.barteksc.pdfviewer.test:attr/materialSearchViewStyle = 0x7f0302cf
com.github.barteksc.pdfviewer.test:color/m3_highlighted_text = 0x7f05008b
com.github.barteksc.pdfviewer.test:attr/materialDividerStyle = 0x7f0302c8
com.github.barteksc.pdfviewer.test:color/m3_filled_icon_button_container_color_selector = 0x7f05008a
com.github.barteksc.pdfviewer.test:color/accent_material_light = 0x7f05001a
com.github.barteksc.pdfviewer.test:attr/region_heightMoreThan = 0x7f03035d
com.github.barteksc.pdfviewer.test:color/m3_fab_efab_foreground_color_selector = 0x7f050088
com.github.barteksc.pdfviewer.test:styleable/NavigationBarView = 0x7f100065
com.github.barteksc.pdfviewer.test:id/mtrl_motion_snapshot_view = 0x7f080101
com.github.barteksc.pdfviewer.test:color/m3_fab_efab_background_color_selector = 0x7f050087
com.github.barteksc.pdfviewer.test:color/m3_dynamic_default_color_primary_text = 0x7f050080
com.github.barteksc.pdfviewer.test:color/m3_assist_chip_icon_tint_color = 0x7f050060
com.github.barteksc.pdfviewer.test:color/mtrl_fab_bg_color_selector = 0x7f0502ac
com.github.barteksc.pdfviewer.test:color/design_default_color_primary = 0x7f050045
com.github.barteksc.pdfviewer.test:id/counterclockwise = 0x7f080072
com.github.barteksc.pdfviewer.test:attr/tabPaddingBottom = 0x7f0303db
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500d1
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f0f024e
com.github.barteksc.pdfviewer.test:color/m3_default_color_secondary_text = 0x7f05007a
com.github.barteksc.pdfviewer.test:drawable/abc_action_bar_item_background_material = 0x7f070029
com.github.barteksc.pdfviewer.test:color/m3_default_color_primary_text = 0x7f050079
com.github.barteksc.pdfviewer.test:color/m3_dark_primary_text_disable_only = 0x7f050078
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f0f03ed
com.github.barteksc.pdfviewer.test:attr/itemStrokeWidth = 0x7f030237
com.github.barteksc.pdfviewer.test:attr/fontStyle = 0x7f0301e9
com.github.barteksc.pdfviewer.test:attr/layout_constraintBottom_toBottomOf = 0x7f030256
com.github.barteksc.pdfviewer.test:color/m3_dark_highlighted_text = 0x7f050076
com.github.barteksc.pdfviewer.test:color/m3_dark_default_color_secondary_text = 0x7f050075
com.github.barteksc.pdfviewer.test:color/m3_dark_default_color_primary_text = 0x7f050074
com.github.barteksc.pdfviewer.test:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060238
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0074
com.github.barteksc.pdfviewer.test:attr/layout_constraintLeft_toLeftOf = 0x7f030269
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral90 = 0x7f050107
com.github.barteksc.pdfviewer.test:attr/passwordToggleEnabled = 0x7f030339
com.github.barteksc.pdfviewer.test:attr/actionModeCloseContentDescription = 0x7f030012
com.github.barteksc.pdfviewer.test:attr/rippleColor = 0x7f030362
com.github.barteksc.pdfviewer.test:color/m3_calendar_item_stroke_color = 0x7f050069
com.github.barteksc.pdfviewer.test:color/m3_text_button_ripple_color_selector = 0x7f0501eb
com.github.barteksc.pdfviewer.test:color/m3_hint_foreground = 0x7f05008c
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral100 = 0x7f0500f8
com.github.barteksc.pdfviewer.test:color/m3_button_ripple_color_selector = 0x7f050067
com.github.barteksc.pdfviewer.test:dimen/m3_chip_disabled_translation_z = 0x7f0600f5
com.github.barteksc.pdfviewer.test:anim/abc_fade_in = 0x7f010000
com.github.barteksc.pdfviewer.test:color/m3_assist_chip_stroke_color = 0x7f050061
com.github.barteksc.pdfviewer.test:color/foreground_material_light = 0x7f05005c
com.github.barteksc.pdfviewer.test:dimen/design_bottom_sheet_peek_height_min = 0x7f06006d
com.github.barteksc.pdfviewer.test:layout/design_navigation_item_subheader = 0x7f0b0027
com.github.barteksc.pdfviewer.test:color/error_color_material_light = 0x7f05005a
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f0f0131
com.github.barteksc.pdfviewer.test:attr/colorOutline = 0x7f0300f9
com.github.barteksc.pdfviewer.test:styleable/MotionScene = 0x7f100062
com.github.barteksc.pdfviewer.test:color/dim_foreground_material_light = 0x7f050058
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f0f010a
com.github.barteksc.pdfviewer.test:color/m3_chip_background_color = 0x7f050070
com.github.barteksc.pdfviewer.test:attr/dropdownListPreferredItemHeight = 0x7f03017b
com.github.barteksc.pdfviewer.test:color/dim_foreground_disabled_material_dark = 0x7f050055
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_extra_long4 = 0x7f090012
com.github.barteksc.pdfviewer.test:attr/badgeRadius = 0x7f030050
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f0f008f
com.github.barteksc.pdfviewer.test:color/abc_hint_foreground_material_light = 0x7f050008
com.github.barteksc.pdfviewer.test:color/design_snackbar_background_color = 0x7f050054
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f0f0284
com.github.barteksc.pdfviewer.test:dimen/m3_snackbar_margin = 0x7f0601e8
com.github.barteksc.pdfviewer.test:id/transitionToEnd = 0x7f0801a6
com.github.barteksc.pdfviewer.test:color/m3_dynamic_hint_foreground = 0x7f050083
com.github.barteksc.pdfviewer.test:attr/subtitleCentered = 0x7f0303be
com.github.barteksc.pdfviewer.test:color/design_fab_stroke_end_outer_color = 0x7f050050
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_icon_color = 0x7f0c012a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_assist_chip_container_height = 0x7f0600fa
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f01bd
com.github.barteksc.pdfviewer.test:color/design_fab_stroke_end_inner_color = 0x7f05004f
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary0 = 0x7f050135
com.github.barteksc.pdfviewer.test:color/primary_text_default_material_dark = 0x7f0502d5
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error30 = 0x7f0500ed
com.github.barteksc.pdfviewer.test:integer/m3_sys_shape_corner_small_corner_family = 0x7f090025
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_secondary = 0x7f050160
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_large_material = 0x7f060048
com.github.barteksc.pdfviewer.test:color/design_default_color_surface = 0x7f05004a
com.github.barteksc.pdfviewer.test:dimen/m3_large_fab_size = 0x7f0601b4
com.github.barteksc.pdfviewer.test:attr/enforceTextAppearance = 0x7f030190
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500a2
com.github.barteksc.pdfviewer.test:color/bright_foreground_material_dark = 0x7f050025
com.github.barteksc.pdfviewer.test:color/design_default_color_on_surface = 0x7f050044
com.github.barteksc.pdfviewer.test:attr/simpleItemLayout = 0x7f03038e
com.github.barteksc.pdfviewer.test:color/abc_tint_edittext = 0x7f050015
com.github.barteksc.pdfviewer.test:attr/layout_goneMarginStart = 0x7f030282
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500b3
com.github.barteksc.pdfviewer.test:color/design_default_color_on_background = 0x7f050040
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_padding_left = 0x7f06025f
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f0f03a0
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500e4
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Snackbar = 0x7f0f043a
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat = 0x7f0f004b
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_secondary_variant = 0x7f05003c
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_on_surface = 0x7f050037
com.github.barteksc.pdfviewer.test:attr/iconTint = 0x7f03020f
com.github.barteksc.pdfviewer.test:attr/autoAdjustToWithinGrandparentBounds = 0x7f030039
com.github.barteksc.pdfviewer.test:attr/suffixTextColor = 0x7f0303c4
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_on_error = 0x7f050034
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.github.barteksc.pdfviewer.test:styleable/State = 0x7f10007d
com.github.barteksc.pdfviewer.test:attr/layout_constraintTop_toBottomOf = 0x7f030272
com.github.barteksc.pdfviewer.test:color/cardview_light_background = 0x7f05002c
com.github.barteksc.pdfviewer.test:color/cardview_dark_background = 0x7f05002b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060112
com.github.barteksc.pdfviewer.test:attr/tabUnboundedRipple = 0x7f0303e6
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary80 = 0x7f05013e
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f0277
com.github.barteksc.pdfviewer.test:color/call_notification_answer_color = 0x7f050029
com.github.barteksc.pdfviewer.test:color/button_material_dark = 0x7f050027
com.github.barteksc.pdfviewer.test:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.github.barteksc.pdfviewer.test:attr/colorPrimary = 0x7f0300fb
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_bright = 0x7f050163
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_label_padding = 0x7f0602df
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_container_color = 0x7f0c014f
com.github.barteksc.pdfviewer.test:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.github.barteksc.pdfviewer.test:color/design_fab_stroke_top_outer_color = 0x7f050052
com.github.barteksc.pdfviewer.test:color/bright_foreground_inverse_material_dark = 0x7f050023
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Title.Text = 0x7f0f012a
com.github.barteksc.pdfviewer.test:attr/layout_scrollEffect = 0x7f030287
com.github.barteksc.pdfviewer.test:color/m3_card_ripple_color = 0x7f05006b
com.github.barteksc.pdfviewer.test:color/background_material_dark = 0x7f05001f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f0f0429
com.github.barteksc.pdfviewer.test:id/decelerate = 0x7f080078
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_body_2_material = 0x7f060040
com.github.barteksc.pdfviewer.test:attr/maxAcceleration = 0x7f0302d7
com.github.barteksc.pdfviewer.test:color/bright_foreground_inverse_material_light = 0x7f050024
com.github.barteksc.pdfviewer.test:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_inset_medium = 0x7f0602cf
com.github.barteksc.pdfviewer.test:layout/mtrl_search_bar = 0x7f0b005d
com.github.barteksc.pdfviewer.test:attr/borderWidth = 0x7f03006f
com.github.barteksc.pdfviewer.test:attr/itemActiveIndicatorStyle = 0x7f030220
com.github.barteksc.pdfviewer.test:color/accent_material_dark = 0x7f050019
com.github.barteksc.pdfviewer.test:attr/fontProviderPackage = 0x7f0301e6
com.github.barteksc.pdfviewer.test:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300d8
com.github.barteksc.pdfviewer.test:attr/boxStrokeWidth = 0x7f030080
com.github.barteksc.pdfviewer.test:attr/backgroundTintMode = 0x7f03004d
com.github.barteksc.pdfviewer.test:color/abc_secondary_text_material_light = 0x7f050012
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f014a
com.github.barteksc.pdfviewer.test:color/abc_search_url_text_normal = 0x7f05000e
com.github.barteksc.pdfviewer.test:attr/dividerHorizontal = 0x7f030163
com.github.barteksc.pdfviewer.test:drawable/design_fab_background = 0x7f070085
com.github.barteksc.pdfviewer.test:color/abc_hint_foreground_material_dark = 0x7f050007
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.IconButton.Outlined = 0x7f0f0353
com.github.barteksc.pdfviewer.test:color/m3_sys_color_on_secondary_fixed = 0x7f0501d9
com.github.barteksc.pdfviewer.test:attr/materialCardViewFilledStyle = 0x7f0302c1
com.github.barteksc.pdfviewer.test:dimen/design_navigation_icon_size = 0x7f060077
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0171
com.github.barteksc.pdfviewer.test:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.github.barteksc.pdfviewer.test:attr/forceApplySystemWindowInsetTop = 0x7f0301ec
com.github.barteksc.pdfviewer.test:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.github.barteksc.pdfviewer.test:color/abc_decor_view_status_guard_light = 0x7f050006
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f02f3
com.github.barteksc.pdfviewer.test:attr/listChoiceBackgroundIndicator = 0x7f030291
com.github.barteksc.pdfviewer.test:styleable/ShapeAppearance = 0x7f100076
com.github.barteksc.pdfviewer.test:attr/overlapAnchor = 0x7f030329
com.github.barteksc.pdfviewer.test:color/abc_decor_view_status_guard = 0x7f050005
com.github.barteksc.pdfviewer.test:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700b0
com.github.barteksc.pdfviewer.test:color/abc_color_highlight_material = 0x7f050004
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_shape_inset_start = 0x7f0601bd
com.github.barteksc.pdfviewer.test:dimen/m3_chip_elevated_elevation = 0x7f0600f7
com.github.barteksc.pdfviewer.test:string/mtrl_checkbox_state_description_checked = 0x7f0e0065
com.github.barteksc.pdfviewer.test:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.github.barteksc.pdfviewer.test:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f0153
com.github.barteksc.pdfviewer.test:attr/motionDurationMedium2 = 0x7f0302fa
com.github.barteksc.pdfviewer.test:id/screen = 0x7f08014d
com.github.barteksc.pdfviewer.test:attr/navigationIconTint = 0x7f030319
com.github.barteksc.pdfviewer.test:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary60 = 0x7f050122
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_shape_inset_top = 0x7f0601be
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0601f8
com.github.barteksc.pdfviewer.test:attr/colorOnSecondary = 0x7f0300ee
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500d3
com.github.barteksc.pdfviewer.test:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.github.barteksc.pdfviewer.test:attr/borderlessButtonStyle = 0x7f030070
com.github.barteksc.pdfviewer.test:color/mtrl_switch_track_tint = 0x7f0502c3
com.github.barteksc.pdfviewer.test:attr/yearTodayStyle = 0x7f030484
com.github.barteksc.pdfviewer.test:attr/yearSelectedStyle = 0x7f030482
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.Display = 0x7f0f0450
com.github.barteksc.pdfviewer.test:layout/material_timepicker_dialog = 0x7f0b003d
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral50 = 0x7f050209
com.github.barteksc.pdfviewer.test:attr/tooltipFrameBackground = 0x7f03044d
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0602f5
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_year_horizontal_padding = 0x7f060291
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f0f0427
com.github.barteksc.pdfviewer.test:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f060236
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f0f03ae
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015b
com.github.barteksc.pdfviewer.test:attr/itemShapeInsetTop = 0x7f030234
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadline2 = 0x7f0303f7
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error40 = 0x7f0500ee
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f02ec
com.github.barteksc.pdfviewer.test:dimen/design_bottom_sheet_modal_elevation = 0x7f06006c
com.github.barteksc.pdfviewer.test:attr/trackTint = 0x7f03045e
com.github.barteksc.pdfviewer.test:id/progress_circular = 0x7f08013c
com.github.barteksc.pdfviewer.test:attr/windowNoTitle = 0x7f030481
com.github.barteksc.pdfviewer.test:id/titleDividerNoCustom = 0x7f0801a0
com.github.barteksc.pdfviewer.test:dimen/m3_fab_translation_z_pressed = 0x7f0601b2
com.github.barteksc.pdfviewer.test:attr/materialTimePickerTitleStyle = 0x7f0302d6
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_3 = 0x7f080027
com.github.barteksc.pdfviewer.test:attr/thickness = 0x7f03041f
com.github.barteksc.pdfviewer.test:attr/colorSwitchThumbNormal = 0x7f030112
com.github.barteksc.pdfviewer.test:color/m3_radiobutton_ripple_tint = 0x7f05009b
com.github.barteksc.pdfviewer.test:attr/windowMinWidthMajor = 0x7f03047f
com.github.barteksc.pdfviewer.test:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.github.barteksc.pdfviewer.test:attr/windowFixedHeightMinor = 0x7f03047c
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant99 = 0x7f05011a
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f06020b
com.github.barteksc.pdfviewer.test:id/navigation_bar_item_icon_view = 0x7f08010f
com.github.barteksc.pdfviewer.test:attr/windowActionBar = 0x7f030478
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_background = 0x7f050261
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f0f0187
com.github.barteksc.pdfviewer.test:attr/waveVariesBy = 0x7f030477
com.github.barteksc.pdfviewer.test:styleable/AppCompatSeekBar = 0x7f10000f
com.github.barteksc.pdfviewer.test:dimen/m3_small_fab_size = 0x7f0601e6
com.github.barteksc.pdfviewer.test:color/bright_foreground_material_light = 0x7f050026
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary0 = 0x7f05021d
com.github.barteksc.pdfviewer.test:attr/waveOffset = 0x7f030474
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_container = 0x7f050164
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_25 = 0x7f080022
com.github.barteksc.pdfviewer.test:attr/waveDecay = 0x7f030473
com.github.barteksc.pdfviewer.test:attr/titleEnabled = 0x7f03043b
com.github.barteksc.pdfviewer.test:attr/warmth = 0x7f030472
com.github.barteksc.pdfviewer.test:attr/contentScrim = 0x7f03012d
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f0f005e
com.github.barteksc.pdfviewer.test:attr/viewInflaterClass = 0x7f03046f
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f0f0236
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500aa
com.github.barteksc.pdfviewer.test:attr/verticalOffset = 0x7f03046d
com.github.barteksc.pdfviewer.test:id/textinput_counter = 0x7f080198
com.github.barteksc.pdfviewer.test:attr/buttonBarStyle = 0x7f030087
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501b2
com.github.barteksc.pdfviewer.test:attr/useDrawerArrowDrawable = 0x7f03046a
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f0f0072
com.github.barteksc.pdfviewer.test:string/call_notification_decline_action = 0x7f0e0025
com.github.barteksc.pdfviewer.test:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017a
com.github.barteksc.pdfviewer.test:color/m3_fab_ripple_color_selector = 0x7f050089
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadlineLarge = 0x7f0303fc
com.github.barteksc.pdfviewer.test:attr/trackHeight = 0x7f03045c
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f0f017e
com.github.barteksc.pdfviewer.test:attr/layout_constraintDimensionRatio = 0x7f03025b
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceCornerMedium = 0x7f03037a
com.github.barteksc.pdfviewer.test:attr/useCompatPadding = 0x7f030469
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bf
com.github.barteksc.pdfviewer.test:dimen/abc_search_view_preferred_width = 0x7f060037
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f01ac
com.github.barteksc.pdfviewer.test:attr/triggerSlack = 0x7f030467
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_disabled_z = 0x7f060255
com.github.barteksc.pdfviewer.test:id/pathRelative = 0x7f080135
com.github.barteksc.pdfviewer.test:attr/triggerId = 0x7f030465
com.github.barteksc.pdfviewer.test:dimen/mtrl_bottomappbar_height = 0x7f060251
com.github.barteksc.pdfviewer.test:attr/transitionShapeAppearance = 0x7f030464
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f0f0092
com.github.barteksc.pdfviewer.test:attr/layout_collapseMode = 0x7f03024f
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001c
com.github.barteksc.pdfviewer.test:attr/transitionPathRotate = 0x7f030463
com.github.barteksc.pdfviewer.test:attr/transitionFlags = 0x7f030462
com.github.barteksc.pdfviewer.test:attr/showMotionSpec = 0x7f030387
com.github.barteksc.pdfviewer.test:attr/trackThickness = 0x7f03045d
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f0f0423
com.github.barteksc.pdfviewer.test:attr/trackDecorationTintMode = 0x7f03045b
com.github.barteksc.pdfviewer.test:attr/tabSelectedTextAppearance = 0x7f0303e1
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f0f038a
com.github.barteksc.pdfviewer.test:attr/trackDecorationTint = 0x7f03045a
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceSmallComponent = 0x7f03037f
com.github.barteksc.pdfviewer.test:attr/materialThemeOverlay = 0x7f0302d3
com.github.barteksc.pdfviewer.test:attr/trackDecoration = 0x7f030459
com.github.barteksc.pdfviewer.test:attr/trackColor = 0x7f030455
com.github.barteksc.pdfviewer.test:attr/track = 0x7f030454
com.github.barteksc.pdfviewer.test:id/embed = 0x7f080096
com.github.barteksc.pdfviewer.test:attr/touchAnchorSide = 0x7f030452
com.github.barteksc.pdfviewer.test:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f000e
com.github.barteksc.pdfviewer.test:attr/touchAnchorId = 0x7f030451
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_elevation = 0x7f0602a2
com.github.barteksc.pdfviewer.test:string/searchview_clear_text_content_description = 0x7f0e00a1
com.github.barteksc.pdfviewer.test:string/mtrl_picker_cancel = 0x7f0e0070
com.github.barteksc.pdfviewer.test:attr/topInsetScrimEnabled = 0x7f030450
com.github.barteksc.pdfviewer.test:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700ae
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f0f0183
com.github.barteksc.pdfviewer.test:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.github.barteksc.pdfviewer.test:attr/tooltipText = 0x7f03044f
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f0f0165
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500b0
com.github.barteksc.pdfviewer.test:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.github.barteksc.pdfviewer.test:attr/tooltipStyle = 0x7f03044e
com.github.barteksc.pdfviewer.test:attr/toolbarStyle = 0x7f03044a
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0316
com.github.barteksc.pdfviewer.test:anim/abc_slide_out_top = 0x7f010009
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f0f015d
com.github.barteksc.pdfviewer.test:attr/collapsingToolbarLayoutMediumSize = 0x7f0300d7
com.github.barteksc.pdfviewer.test:anim/abc_slide_in_top = 0x7f010007
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0302
com.github.barteksc.pdfviewer.test:attr/tabMaxWidth = 0x7f0303d7
com.github.barteksc.pdfviewer.test:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f050096
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_27 = 0x7f080024
com.github.barteksc.pdfviewer.test:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.github.barteksc.pdfviewer.test:id/deltaRelative = 0x7f08007c
com.github.barteksc.pdfviewer.test:attr/materialIconButtonFilledTonalStyle = 0x7f0302ca
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_on_secondary = 0x7f050036
com.github.barteksc.pdfviewer.test:attr/counterMaxLength = 0x7f03013e
com.github.barteksc.pdfviewer.test:attr/layout_constraintRight_toRightOf = 0x7f03026d
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f0601fe
com.github.barteksc.pdfviewer.test:attr/titleTextColor = 0x7f030444
com.github.barteksc.pdfviewer.test:id/search_edit_frame = 0x7f080157
com.github.barteksc.pdfviewer.test:attr/bottomSheetDragHandleStyle = 0x7f030075
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary10 = 0x7f050136
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06011e
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_display_1_material = 0x7f060043
com.github.barteksc.pdfviewer.test:attr/titleMargin = 0x7f03043c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f0f0340
com.github.barteksc.pdfviewer.test:layout/design_text_input_end_icon = 0x7f0b002a
com.github.barteksc.pdfviewer.test:attr/titleCollapseMode = 0x7f03043a
com.github.barteksc.pdfviewer.test:attr/titleCentered = 0x7f030439
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f0f0076
com.github.barteksc.pdfviewer.test:attr/textAppearanceBody2 = 0x7f0303ed
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant80 = 0x7f050219
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.ActionMode = 0x7f0f00fe
com.github.barteksc.pdfviewer.test:attr/tintNavigationIcon = 0x7f030437
com.github.barteksc.pdfviewer.test:attr/hoveredFocusedTranslationZ = 0x7f030208
com.github.barteksc.pdfviewer.test:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f06024e
com.github.barteksc.pdfviewer.test:attr/chipStrokeColor = 0x7f0300bc
com.github.barteksc.pdfviewer.test:attr/tintMode = 0x7f030436
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012b
com.github.barteksc.pdfviewer.test:color/abc_tint_default = 0x7f050014
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00bc
com.github.barteksc.pdfviewer.test:dimen/fastscroll_minimum_range = 0x7f060092
com.github.barteksc.pdfviewer.test:attr/tint = 0x7f030435
com.github.barteksc.pdfviewer.test:attr/buttonBarButtonStyle = 0x7f030083
com.github.barteksc.pdfviewer.test:attr/tickRadiusActive = 0x7f030432
com.github.barteksc.pdfviewer.test:attr/tickMarkTintMode = 0x7f030431
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_long2 = 0x7f090014
com.github.barteksc.pdfviewer.test:color/material_grey_300 = 0x7f050245
com.github.barteksc.pdfviewer.test:attr/tickColorInactive = 0x7f03042e
com.github.barteksc.pdfviewer.test:attr/thumbTintMode = 0x7f03042b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060113
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f031b
com.github.barteksc.pdfviewer.test:dimen/material_emphasis_medium = 0x7f06022e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_background = 0x7f05014b
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f0f03e8
com.github.barteksc.pdfviewer.test:attr/keyboardIcon = 0x7f03023f
com.github.barteksc.pdfviewer.test:attr/thumbTint = 0x7f03042a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error99 = 0x7f0500f5
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f0f023e
com.github.barteksc.pdfviewer.test:attr/fontProviderCerts = 0x7f0301e3
com.github.barteksc.pdfviewer.test:attr/thumbStrokeColor = 0x7f030427
com.github.barteksc.pdfviewer.test:attr/thumbRadius = 0x7f030426
com.github.barteksc.pdfviewer.test:id/chains = 0x7f080060
com.github.barteksc.pdfviewer.test:attr/fabAnimationMode = 0x7f0301b2
com.github.barteksc.pdfviewer.test:attr/windowFixedWidthMajor = 0x7f03047d
com.github.barteksc.pdfviewer.test:string/mtrl_picker_a11y_prev_month = 0x7f0e006c
com.github.barteksc.pdfviewer.test:color/design_box_stroke_color = 0x7f050030
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_text_input_padding_top = 0x7f06028c
com.github.barteksc.pdfviewer.test:attr/thumbElevation = 0x7f030421
com.github.barteksc.pdfviewer.test:attr/buttonGravity = 0x7f030089
com.github.barteksc.pdfviewer.test:attr/textLocale = 0x7f03041c
com.github.barteksc.pdfviewer.test:attr/textInputStyle = 0x7f03041b
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f014f
com.github.barteksc.pdfviewer.test:attr/colorError = 0x7f0300e2
com.github.barteksc.pdfviewer.test:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007e
com.github.barteksc.pdfviewer.test:attr/badgeShapeAppearanceOverlay = 0x7f030052
com.github.barteksc.pdfviewer.test:attr/tickMark = 0x7f03042f
com.github.barteksc.pdfviewer.test:attr/textInputOutlinedStyle = 0x7f03041a
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CardView = 0x7f0f03ff
com.github.barteksc.pdfviewer.test:drawable/ic_m3_chip_close = 0x7f070096
com.github.barteksc.pdfviewer.test:attr/autoSizeTextType = 0x7f030040
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
com.github.barteksc.pdfviewer.test:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f030419
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500bc
com.github.barteksc.pdfviewer.test:attr/fabAnchorMode = 0x7f0301b1
com.github.barteksc.pdfviewer.test:attr/triggerReceiver = 0x7f030466
com.github.barteksc.pdfviewer.test:drawable/$avd_hide_password__1 = 0x7f070001
com.github.barteksc.pdfviewer.test:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f0f0345
com.github.barteksc.pdfviewer.test:id/all = 0x7f080046
com.github.barteksc.pdfviewer.test:attr/textInputLayoutFocusedRectEnabled = 0x7f030417
com.github.barteksc.pdfviewer.test:color/m3_button_outline_color_selector = 0x7f050065
com.github.barteksc.pdfviewer.test:attr/textInputFilledExposedDropdownMenuStyle = 0x7f030415
com.github.barteksc.pdfviewer.test:attr/textEndPadding = 0x7f030413
com.github.barteksc.pdfviewer.test:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007c
com.github.barteksc.pdfviewer.test:attr/textAppearanceTitleMedium = 0x7f03040f
com.github.barteksc.pdfviewer.test:color/material_personalized_primary_inverse_text_disable_only = 0x7f05028b
com.github.barteksc.pdfviewer.test:attr/layout_constraintWidth_min = 0x7f030279
com.github.barteksc.pdfviewer.test:attr/textAppearanceSubtitle2 = 0x7f03040d
com.github.barteksc.pdfviewer.test:attr/textAppearanceSmallPopupMenu = 0x7f03040b
com.github.barteksc.pdfviewer.test:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070035
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light = 0x7f0f0219
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_text_size = 0x7f0601d9
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.SideSheetDialog = 0x7f0f0241
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary80 = 0x7f050233
com.github.barteksc.pdfviewer.test:attr/textAppearanceSearchResultTitle = 0x7f03040a
com.github.barteksc.pdfviewer.test:drawable/abc_edit_text_material = 0x7f07003c
com.github.barteksc.pdfviewer.test:attr/textAppearanceSearchResultSubtitle = 0x7f030409
com.github.barteksc.pdfviewer.test:attr/cardUseCompatPadding = 0x7f030099
com.github.barteksc.pdfviewer.test:attr/subheaderInsetStart = 0x7f0303ba
com.github.barteksc.pdfviewer.test:color/design_default_color_background = 0x7f05003e
com.github.barteksc.pdfviewer.test:attr/textAppearancePopupMenuHeader = 0x7f030408
com.github.barteksc.pdfviewer.test:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f030305
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060142
com.github.barteksc.pdfviewer.test:attr/textAppearanceListItemSecondary = 0x7f030405
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary60 = 0x7f05013c
com.github.barteksc.pdfviewer.test:attr/textAppearanceLabelMedium = 0x7f030400
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f06019d
com.github.barteksc.pdfviewer.test:id/BOTTOM_END = 0x7f080001
com.github.barteksc.pdfviewer.test:color/highlighted_text_material_light = 0x7f05005e
com.github.barteksc.pdfviewer.test:attr/elevation = 0x7f030181
com.github.barteksc.pdfviewer.test:attr/toolbarSurfaceStyle = 0x7f03044b
com.github.barteksc.pdfviewer.test:color/design_default_color_error = 0x7f05003f
com.github.barteksc.pdfviewer.test:attr/textAppearanceDisplaySmall = 0x7f0303f5
com.github.barteksc.pdfviewer.test:attr/textAppearanceDisplayLarge = 0x7f0303f3
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CompoundButton.CheckBox = 0x7f0f0375
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f0f0169
com.github.barteksc.pdfviewer.test:attr/logoAdjustViewBounds = 0x7f0302a1
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary50 = 0x7f050230
com.github.barteksc.pdfviewer.test:attr/addElevationShadow = 0x7f030026
com.github.barteksc.pdfviewer.test:color/design_default_color_on_primary = 0x7f050042
com.github.barteksc.pdfviewer.test:attr/layout_constrainedWidth = 0x7f030252
com.github.barteksc.pdfviewer.test:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d2
com.github.barteksc.pdfviewer.test:attr/statusBarForeground = 0x7f0303b3
com.github.barteksc.pdfviewer.test:attr/barrierDirection = 0x7f030061
com.github.barteksc.pdfviewer.test:id/elastic = 0x7f080095
com.github.barteksc.pdfviewer.test:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014f
com.github.barteksc.pdfviewer.test:attr/textAppearanceButton = 0x7f0303f1
com.github.barteksc.pdfviewer.test:styleable/ScrollingViewBehavior_Layout = 0x7f100073
com.github.barteksc.pdfviewer.test:attr/hintAnimationEnabled = 0x7f030200
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016e
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500ab
com.github.barteksc.pdfviewer.test:attr/textAppearanceSubtitle1 = 0x7f03040c
com.github.barteksc.pdfviewer.test:drawable/abc_seekbar_tick_mark_material = 0x7f070063
com.github.barteksc.pdfviewer.test:color/m3_sys_color_primary_fixed = 0x7f0501dd
com.github.barteksc.pdfviewer.test:attr/itemPaddingBottom = 0x7f03022b
com.github.barteksc.pdfviewer.test:attr/extraMultilineHeightEnabled = 0x7f0301ae
com.github.barteksc.pdfviewer.test:attr/textAppearanceBody1 = 0x7f0303ec
com.github.barteksc.pdfviewer.test:styleable/ActionBarLayout = 0x7f100001
com.github.barteksc.pdfviewer.test:drawable/tooltip_frame_light = 0x7f0700e6
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0089
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_primary = 0x7f050038
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant90 = 0x7f05021a
com.github.barteksc.pdfviewer.test:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.github.barteksc.pdfviewer.test:attr/motionEasingStandardDecelerateInterpolator = 0x7f03030b
com.github.barteksc.pdfviewer.test:attr/textAllCaps = 0x7f0303eb
com.github.barteksc.pdfviewer.test:attr/dialogPreferredPadding = 0x7f03015e
com.github.barteksc.pdfviewer.test:attr/tabTextColor = 0x7f0303e5
com.github.barteksc.pdfviewer.test:color/button_material_light = 0x7f050028
com.github.barteksc.pdfviewer.test:attr/switchPadding = 0x7f0303c7
com.github.barteksc.pdfviewer.test:attr/tabTextAppearance = 0x7f0303e4
com.github.barteksc.pdfviewer.test:attr/chipIconSize = 0x7f0300b2
com.github.barteksc.pdfviewer.test:attr/tabSelectedTextColor = 0x7f0303e2
com.github.barteksc.pdfviewer.test:integer/mtrl_switch_thumb_motion_duration = 0x7f090036
com.github.barteksc.pdfviewer.test:attr/chipStartPadding = 0x7f0300bb
com.github.barteksc.pdfviewer.test:drawable/notification_bg = 0x7f0700d9
com.github.barteksc.pdfviewer.test:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.github.barteksc.pdfviewer.test:attr/tabSecondaryStyle = 0x7f0303e0
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_background = 0x7f050031
com.github.barteksc.pdfviewer.test:attr/tabPadding = 0x7f0303da
com.github.barteksc.pdfviewer.test:attr/sb_indicatorColor = 0x7f030368
com.github.barteksc.pdfviewer.test:id/legacy = 0x7f0800cc
com.github.barteksc.pdfviewer.test:attr/tabMinWidth = 0x7f0303d8
com.github.barteksc.pdfviewer.test:attr/tabInlineLabel = 0x7f0303d6
com.github.barteksc.pdfviewer.test:drawable/$m3_avd_hide_password__1 = 0x7f070007
com.github.barteksc.pdfviewer.test:style/Base.Widget.Design.TabLayout = 0x7f0f00fc
com.github.barteksc.pdfviewer.test:attr/textAppearanceListItemSmall = 0x7f030406
com.github.barteksc.pdfviewer.test:attr/tabIndicatorHeight = 0x7f0303d5
com.github.barteksc.pdfviewer.test:attr/actionBarStyle = 0x7f030005
com.github.barteksc.pdfviewer.test:attr/colorOnTertiaryFixed = 0x7f0300f7
com.github.barteksc.pdfviewer.test:drawable/abc_item_background_holo_dark = 0x7f07004a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060106
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f0028
com.github.barteksc.pdfviewer.test:id/tag_window_insets_animation_callback = 0x7f08018d
com.github.barteksc.pdfviewer.test:attr/iconPadding = 0x7f03020c
com.github.barteksc.pdfviewer.test:attr/tabIndicator = 0x7f0303cf
com.github.barteksc.pdfviewer.test:attr/tabIconTint = 0x7f0303cd
com.github.barteksc.pdfviewer.test:attr/tabContentStart = 0x7f0303cb
com.github.barteksc.pdfviewer.test:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f0f01da
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602a6
com.github.barteksc.pdfviewer.test:attr/tooltipForegroundColor = 0x7f03044c
com.github.barteksc.pdfviewer.test:color/material_personalized_color_error_container = 0x7f050260
com.github.barteksc.pdfviewer.test:attr/materialAlertDialogTitleIconStyle = 0x7f0302ab
com.github.barteksc.pdfviewer.test:attr/state_liftable = 0x7f0303af
com.github.barteksc.pdfviewer.test:styleable/FloatingActionButton_Behavior_Layout = 0x7f100031
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f0f0067
com.github.barteksc.pdfviewer.test:attr/thumbTextPadding = 0x7f030429
com.github.barteksc.pdfviewer.test:attr/switchTextAppearance = 0x7f0303c9
com.github.barteksc.pdfviewer.test:attr/switchStyle = 0x7f0303c8
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_container_highest = 0x7f0501d0
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_text_btn_padding_left = 0x7f060266
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00b1
com.github.barteksc.pdfviewer.test:layout/support_simple_spinner_dropdown_item = 0x7f0b0068
com.github.barteksc.pdfviewer.test:integer/mtrl_badge_max_character_count = 0x7f09002d
com.github.barteksc.pdfviewer.test:attr/switchMinWidth = 0x7f0303c6
com.github.barteksc.pdfviewer.test:attr/suggestionRowLayout = 0x7f0303c5
com.github.barteksc.pdfviewer.test:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f06010a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_error = 0x7f0501bc
com.github.barteksc.pdfviewer.test:attr/suffixTextAppearance = 0x7f0303c3
com.github.barteksc.pdfviewer.test:dimen/mtrl_shape_corner_size_small_component = 0x7f0602dd
com.github.barteksc.pdfviewer.test:attr/textAppearanceTitleSmall = 0x7f030410
com.github.barteksc.pdfviewer.test:dimen/m3_btn_max_width = 0x7f0600d8
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ButtonBar = 0x7f0f02f6
com.github.barteksc.pdfviewer.test:attr/subtitleTextAppearance = 0x7f0303bf
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f0f02b5
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500b9
com.github.barteksc.pdfviewer.test:attr/listPreferredItemHeight = 0x7f030299
com.github.barteksc.pdfviewer.test:attr/submitBackground = 0x7f0303bc
com.github.barteksc.pdfviewer.test:attr/subheaderTextAppearance = 0x7f0303bb
com.github.barteksc.pdfviewer.test:color/m3_radiobutton_button_tint = 0x7f05009a
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_title_material = 0x7f06004f
com.github.barteksc.pdfviewer.test:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700ad
com.github.barteksc.pdfviewer.test:attr/subheaderColor = 0x7f0303b8
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary0 = 0x7f0500c2
com.github.barteksc.pdfviewer.test:styleable/ActionMode = 0x7f100004
com.github.barteksc.pdfviewer.test:attr/subMenuArrow = 0x7f0303b7
com.github.barteksc.pdfviewer.test:attr/strokeWidth = 0x7f0303b6
com.github.barteksc.pdfviewer.test:drawable/abc_list_selector_disabled_holo_dark = 0x7f070054
com.github.barteksc.pdfviewer.test:attr/listDividerAlertDialog = 0x7f030294
com.github.barteksc.pdfviewer.test:id/visible_removing_fragment_view_tag = 0x7f0801b9
com.github.barteksc.pdfviewer.test:attr/colorOnSurfaceInverse = 0x7f0300f3
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f02ef
com.github.barteksc.pdfviewer.test:attr/pivotAnchor = 0x7f030343
com.github.barteksc.pdfviewer.test:attr/textAppearanceLabelLarge = 0x7f0303ff
com.github.barteksc.pdfviewer.test:attr/horizontalOffset = 0x7f030206
com.github.barteksc.pdfviewer.test:attr/state_error = 0x7f0303ad
com.github.barteksc.pdfviewer.test:color/material_on_surface_stroke = 0x7f050258
com.github.barteksc.pdfviewer.test:attr/searchIcon = 0x7f03036e
com.github.barteksc.pdfviewer.test:styleable/ConstraintLayout_placeholder = 0x7f100027
com.github.barteksc.pdfviewer.test:attr/tabGravity = 0x7f0303cc
com.github.barteksc.pdfviewer.test:attr/contentPadding = 0x7f030126
com.github.barteksc.pdfviewer.test:attr/state_dragged = 0x7f0303ac
com.github.barteksc.pdfviewer.test:attr/state_collapsible = 0x7f0303ab
com.github.barteksc.pdfviewer.test:style/CardView.Light = 0x7f0f0121
com.github.barteksc.pdfviewer.test:dimen/mtrl_tooltip_minHeight = 0x7f060300
com.github.barteksc.pdfviewer.test:attr/state_collapsed = 0x7f0303aa
com.github.barteksc.pdfviewer.test:attr/state_above_anchor = 0x7f0303a9
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f0f03b2
com.github.barteksc.pdfviewer.test:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601a4
com.github.barteksc.pdfviewer.test:attr/startIconTintMode = 0x7f0303a8
com.github.barteksc.pdfviewer.test:attr/strokeColor = 0x7f0303b5
com.github.barteksc.pdfviewer.test:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.github.barteksc.pdfviewer.test:attr/startIconTint = 0x7f0303a7
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000c
com.github.barteksc.pdfviewer.test:attr/tickColor = 0x7f03042c
com.github.barteksc.pdfviewer.test:attr/paddingTopSystemWindowInsets = 0x7f030333
com.github.barteksc.pdfviewer.test:drawable/abc_textfield_search_material = 0x7f070075
com.github.barteksc.pdfviewer.test:color/m3_bottom_sheet_drag_handle_color = 0x7f050062
com.github.barteksc.pdfviewer.test:attr/cornerFamilyTopRight = 0x7f030136
com.github.barteksc.pdfviewer.test:color/highlighted_text_material_dark = 0x7f05005d
com.github.barteksc.pdfviewer.test:attr/itemRippleColor = 0x7f03022d
com.github.barteksc.pdfviewer.test:color/m3_navigation_item_icon_tint = 0x7f050092
com.github.barteksc.pdfviewer.test:attr/flow_firstHorizontalBias = 0x7f0301cd
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Light.Dialog.Alert = 0x7f0f023d
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f0f0185
com.github.barteksc.pdfviewer.test:attr/region_heightLessThan = 0x7f03035c
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700bb
com.github.barteksc.pdfviewer.test:id/filled = 0x7f0800a2
com.github.barteksc.pdfviewer.test:attr/startIconCheckable = 0x7f0303a2
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_standard_decelerate = 0x7f0e0045
com.github.barteksc.pdfviewer.test:attr/layout_constraintBaseline_creator = 0x7f030253
com.github.barteksc.pdfviewer.test:attr/hintEnabled = 0x7f030201
com.github.barteksc.pdfviewer.test:attr/staggered = 0x7f0303a1
com.github.barteksc.pdfviewer.test:dimen/m3_small_fab_max_image_size = 0x7f0601e5
com.github.barteksc.pdfviewer.test:attr/stackFromEnd = 0x7f0303a0
com.github.barteksc.pdfviewer.test:dimen/abc_action_button_min_width_material = 0x7f06000e
com.github.barteksc.pdfviewer.test:attr/spinnerStyle = 0x7f03039d
com.github.barteksc.pdfviewer.test:attr/spinnerDropDownItemStyle = 0x7f03039c
com.github.barteksc.pdfviewer.test:attr/insetForeground = 0x7f03021b
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0032
com.github.barteksc.pdfviewer.test:attr/spinBars = 0x7f03039b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error100 = 0x7f0500eb
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500d7
com.github.barteksc.pdfviewer.test:id/tag_accessibility_actions = 0x7f080181
com.github.barteksc.pdfviewer.test:attr/colorTertiaryContainer = 0x7f030114
com.github.barteksc.pdfviewer.test:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f05008e
com.github.barteksc.pdfviewer.test:attr/spanCount = 0x7f03039a
com.github.barteksc.pdfviewer.test:id/enterAlways = 0x7f080099
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f060199
com.github.barteksc.pdfviewer.test:attr/snackbarStyle = 0x7f030398
com.github.barteksc.pdfviewer.test:attr/singleSelection = 0x7f030394
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013f
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f0f02d5
com.github.barteksc.pdfviewer.test:attr/percentWidth = 0x7f03033f
com.github.barteksc.pdfviewer.test:attr/singleChoiceItemLayout = 0x7f030392
com.github.barteksc.pdfviewer.test:id/SHOW_ALL = 0x7f080008
com.github.barteksc.pdfviewer.test:attr/expandedTitleMargin = 0x7f0301a0
com.github.barteksc.pdfviewer.test:attr/simpleItemSelectedColor = 0x7f03038f
com.github.barteksc.pdfviewer.test:drawable/avd_show_password = 0x7f070078
com.github.barteksc.pdfviewer.test:drawable/$m3_avd_hide_password__2 = 0x7f070008
com.github.barteksc.pdfviewer.test:color/mtrl_btn_text_color_disabled = 0x7f05029d
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant20 = 0x7f050111
com.github.barteksc.pdfviewer.test:drawable/ic_mtrl_chip_checked_circle = 0x7f070099
com.github.barteksc.pdfviewer.test:attr/motionDurationShort4 = 0x7f030300
com.github.barteksc.pdfviewer.test:attr/sideSheetModalStyle = 0x7f03038d
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000f
com.github.barteksc.pdfviewer.test:attr/shrinkMotionSpec = 0x7f03038b
com.github.barteksc.pdfviewer.test:attr/dividerVertical = 0x7f030168
com.github.barteksc.pdfviewer.test:attr/attributeName = 0x7f030038
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f0f03a5
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary40 = 0x7f05023c
com.github.barteksc.pdfviewer.test:attr/showText = 0x7f030389
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_error = 0x7f050152
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_bright = 0x7f050279
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.IconButton = 0x7f0f0350
com.github.barteksc.pdfviewer.test:attr/ttcIndex = 0x7f030468
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004e
com.github.barteksc.pdfviewer.test:attr/showDelay = 0x7f030385
com.github.barteksc.pdfviewer.test:string/fab_transformation_scrim_behavior = 0x7f0e0031
com.github.barteksc.pdfviewer.test:attr/shouldRemoveExpandedCorners = 0x7f030382
com.github.barteksc.pdfviewer.test:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0107
com.github.barteksc.pdfviewer.test:attr/snackbarTextViewStyle = 0x7f030399
com.github.barteksc.pdfviewer.test:attr/showAsAction = 0x7f030384
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f0f0390
com.github.barteksc.pdfviewer.test:bool/abc_action_bar_embed_tabs = 0x7f040000
com.github.barteksc.pdfviewer.test:color/dim_foreground_disabled_material_light = 0x7f050056
com.github.barteksc.pdfviewer.test:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0117
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_error = 0x7f050032
com.github.barteksc.pdfviewer.test:attr/arcMode = 0x7f030035
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_primary = 0x7f050264
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_black = 0x7f05009c
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary30 = 0x7f05011f
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06010e
com.github.barteksc.pdfviewer.test:dimen/abc_list_item_height_small_material = 0x7f060032
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary100 = 0x7f050137
com.github.barteksc.pdfviewer.test:id/accessibility_action_clickable_span = 0x7f08000f
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents = 0x7f0f0065
com.github.barteksc.pdfviewer.test:color/material_harmonized_color_error_container = 0x7f05024c
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f0f02d4
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0121
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceLargeComponent = 0x7f03037c
com.github.barteksc.pdfviewer.test:string/abc_searchview_description_query = 0x7f0e0014
com.github.barteksc.pdfviewer.test:attr/trackTintMode = 0x7f03045f
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceCornerLarge = 0x7f030379
com.github.barteksc.pdfviewer.test:color/mtrl_card_view_foreground = 0x7f0502a2
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceCornerExtraSmall = 0x7f030378
com.github.barteksc.pdfviewer.test:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070061
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary90 = 0x7f0500cc
com.github.barteksc.pdfviewer.test:id/tabMode = 0x7f080180
com.github.barteksc.pdfviewer.test:attr/toggleCheckedStateOnClick = 0x7f030447
com.github.barteksc.pdfviewer.test:attr/colorOnPrimaryFixedVariant = 0x7f0300ec
com.github.barteksc.pdfviewer.test:attr/shapeAppearance = 0x7f030376
com.github.barteksc.pdfviewer.test:string/abc_menu_function_shortcut_label = 0x7f0e000c
com.github.barteksc.pdfviewer.test:attr/selectableItemBackground = 0x7f030372
com.github.barteksc.pdfviewer.test:id/spacer = 0x7f08016b
com.github.barteksc.pdfviewer.test:attr/sb_indicatorTextColor = 0x7f030369
com.github.barteksc.pdfviewer.test:attr/seekBarStyle = 0x7f030371
com.github.barteksc.pdfviewer.test:drawable/m3_password_eye = 0x7f0700a0
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CardView.Outlined = 0x7f0f0361
com.github.barteksc.pdfviewer.test:string/call_notification_answer_action = 0x7f0e0023
com.github.barteksc.pdfviewer.test:attr/tabIndicatorAnimationDuration = 0x7f0303d0
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral30 = 0x7f050207
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight = 0x7f0f0246
com.github.barteksc.pdfviewer.test:color/m3_dynamic_highlighted_text = 0x7f050082
com.github.barteksc.pdfviewer.test:attr/sb_handlerColor = 0x7f030366
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500ac
com.github.barteksc.pdfviewer.test:color/material_personalized_color_primary_inverse = 0x7f050271
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c0091
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant95 = 0x7f050119
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f00ed
com.github.barteksc.pdfviewer.test:attr/round = 0x7f030363
com.github.barteksc.pdfviewer.test:attr/layout_constraintStart_toStartOf = 0x7f03026f
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060146
com.github.barteksc.pdfviewer.test:attr/recyclerViewStyle = 0x7f03035b
com.github.barteksc.pdfviewer.test:attr/ratingBarStyleSmall = 0x7f03035a
com.github.barteksc.pdfviewer.test:attr/radioButtonStyle = 0x7f030356
com.github.barteksc.pdfviewer.test:interpolator/fast_out_slow_in = 0x7f0a0006
com.github.barteksc.pdfviewer.test:attr/prefixTextColor = 0x7f03034e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_slider_inactive_track_height = 0x7f060182
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f0f0289
com.github.barteksc.pdfviewer.test:attr/popupMenuStyle = 0x7f030349
com.github.barteksc.pdfviewer.test:dimen/m3_chip_icon_size = 0x7f0600f9
com.github.barteksc.pdfviewer.test:attr/colorOnContainerUnchecked = 0x7f0300e6
com.github.barteksc.pdfviewer.test:attr/popupMenuBackground = 0x7f030348
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary70 = 0x7f05013d
com.github.barteksc.pdfviewer.test:string/status_bar_notification_info_overflow = 0x7f0e00a5
com.github.barteksc.pdfviewer.test:attr/placeholderTextAppearance = 0x7f030345
com.github.barteksc.pdfviewer.test:attr/materialCalendarDayOfWeekLabel = 0x7f0302b2
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary100 = 0x7f05021f
com.github.barteksc.pdfviewer.test:attr/backgroundStacked = 0x7f03004b
com.github.barteksc.pdfviewer.test:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0027
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060156
com.github.barteksc.pdfviewer.test:attr/actionOverflowButtonStyle = 0x7f03001f
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00c9
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_surface = 0x7f05003d
com.github.barteksc.pdfviewer.test:attr/percentX = 0x7f030340
com.github.barteksc.pdfviewer.test:id/CTRL = 0x7f080003
com.github.barteksc.pdfviewer.test:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.github.barteksc.pdfviewer.test:color/m3_switch_track_tint = 0x7f05014a
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Dialog = 0x7f0f0264
com.github.barteksc.pdfviewer.test:attr/layout_constraintEnd_toStartOf = 0x7f03025d
com.github.barteksc.pdfviewer.test:attr/passwordToggleTintMode = 0x7f03033b
com.github.barteksc.pdfviewer.test:attr/passwordToggleDrawable = 0x7f030338
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501a2
com.github.barteksc.pdfviewer.test:attr/panelMenuListWidth = 0x7f030336
com.github.barteksc.pdfviewer.test:color/m3_text_button_background_color_selector = 0x7f0501e9
com.github.barteksc.pdfviewer.test:drawable/notification_bg_low = 0x7f0700da
com.github.barteksc.pdfviewer.test:attr/contentInsetEndWithActions = 0x7f030121
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral12 = 0x7f0500f9
com.github.barteksc.pdfviewer.test:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070074
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f0f017c
com.github.barteksc.pdfviewer.test:attr/panelMenuListTheme = 0x7f030335
com.github.barteksc.pdfviewer.test:attr/panelBackground = 0x7f030334
com.github.barteksc.pdfviewer.test:drawable/abc_ic_go_search_api_material = 0x7f070041
com.github.barteksc.pdfviewer.test:attr/statusBarBackground = 0x7f0303b2
com.github.barteksc.pdfviewer.test:id/compress = 0x7f08006a
com.github.barteksc.pdfviewer.test:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f06024f
com.github.barteksc.pdfviewer.test:attr/motionDurationExtraLong2 = 0x7f0302f2
com.github.barteksc.pdfviewer.test:attr/paddingStartSystemWindowInsets = 0x7f030331
com.github.barteksc.pdfviewer.test:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.github.barteksc.pdfviewer.test:id/SHOW_PROGRESS = 0x7f08000a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface = 0x7f050162
com.github.barteksc.pdfviewer.test:attr/extendMotionSpec = 0x7f0301a7
com.github.barteksc.pdfviewer.test:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f0f022c
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.Material3.Dialog = 0x7f0f0081
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a7
com.github.barteksc.pdfviewer.test:attr/minWidth = 0x7f0302e9
com.github.barteksc.pdfviewer.test:attr/tabIndicatorColor = 0x7f0303d2
com.github.barteksc.pdfviewer.test:attr/paddingRightSystemWindowInsets = 0x7f03032f
com.github.barteksc.pdfviewer.test:id/home = 0x7f0800b7
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
com.github.barteksc.pdfviewer.test:attr/sizePercent = 0x7f030395
com.github.barteksc.pdfviewer.test:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060105
com.github.barteksc.pdfviewer.test:attr/measureWithLargestChild = 0x7f0302e1
com.github.barteksc.pdfviewer.test:attr/onShow = 0x7f030327
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.ActionBar = 0x7f0f02e5
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f0f02bf
com.github.barteksc.pdfviewer.test:layout/design_layout_snackbar = 0x7f0b001f
com.github.barteksc.pdfviewer.test:dimen/m3_extended_fab_bottom_padding = 0x7f0601a9
com.github.barteksc.pdfviewer.test:attr/thumbStrokeWidth = 0x7f030428
com.github.barteksc.pdfviewer.test:styleable/ThemeEnforcement = 0x7f100088
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f0f03d5
com.github.barteksc.pdfviewer.test:attr/onPositiveCross = 0x7f030326
com.github.barteksc.pdfviewer.test:integer/m3_sys_shape_corner_full_corner_family = 0x7f090022
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f060161
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral95 = 0x7f05010a
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f0210
com.github.barteksc.pdfviewer.test:attr/onHide = 0x7f030324
com.github.barteksc.pdfviewer.test:styleable/MaterialShape = 0x7f100055
com.github.barteksc.pdfviewer.test:color/m3_button_background_color_selector = 0x7f050063
com.github.barteksc.pdfviewer.test:attr/collapsingToolbarLayoutLargeSize = 0x7f0300d5
com.github.barteksc.pdfviewer.test:attr/actionBarTabBarStyle = 0x7f030006
com.github.barteksc.pdfviewer.test:attr/elevationOverlayAccentColor = 0x7f030182
com.github.barteksc.pdfviewer.test:attr/layout_optimizationLevel = 0x7f030286
com.github.barteksc.pdfviewer.test:attr/numericModifiers = 0x7f030321
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f001d
com.github.barteksc.pdfviewer.test:attr/layout_goneMarginLeft = 0x7f030280
com.github.barteksc.pdfviewer.test:attr/behavior_saveFlags = 0x7f03006c
com.github.barteksc.pdfviewer.test:attr/offsetAlignmentMode = 0x7f030322
com.github.barteksc.pdfviewer.test:attr/nestedScrollViewStyle = 0x7f03031e
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
com.github.barteksc.pdfviewer.test:attr/ratingBarStyle = 0x7f030358
com.github.barteksc.pdfviewer.test:id/left = 0x7f0800ca
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_elevation = 0x7f060256
com.github.barteksc.pdfviewer.test:drawable/m3_selection_control_ripple = 0x7f0700a3
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Toolbar = 0x7f0f00fa
com.github.barteksc.pdfviewer.test:attr/navigationViewStyle = 0x7f03031c
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.HeadlineMedium = 0x7f0f01eb
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_bottom_padding = 0x7f06029f
com.github.barteksc.pdfviewer.test:attr/navigationMode = 0x7f03031a
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0161
com.github.barteksc.pdfviewer.test:attr/suffixText = 0x7f0303c2
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary50 = 0x7f050223
com.github.barteksc.pdfviewer.test:attr/itemPaddingTop = 0x7f03022c
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonTertiaryStyle = 0x7f0301cc
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0072
com.github.barteksc.pdfviewer.test:attr/statusBarScrim = 0x7f0303b4
com.github.barteksc.pdfviewer.test:id/notification_background = 0x7f080118
com.github.barteksc.pdfviewer.test:attr/navigationIcon = 0x7f030318
com.github.barteksc.pdfviewer.test:attr/colorSurfaceInverse = 0x7f030110
com.github.barteksc.pdfviewer.test:color/abc_primary_text_material_light = 0x7f05000c
com.github.barteksc.pdfviewer.test:attr/moveWhenScrollAtTop = 0x7f030315
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f0f03e9
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0033
com.github.barteksc.pdfviewer.test:attr/motionTarget = 0x7f030312
com.github.barteksc.pdfviewer.test:id/stop = 0x7f08017c
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_padding_top_material = 0x7f060025
com.github.barteksc.pdfviewer.test:attr/motionProgress = 0x7f030310
com.github.barteksc.pdfviewer.test:attr/motionStagger = 0x7f030311
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Bridge = 0x7f0f0244
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_track_color = 0x7f0c0141
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary40 = 0x7f05012d
com.github.barteksc.pdfviewer.test:attr/motionPathRotate = 0x7f03030f
com.github.barteksc.pdfviewer.test:attr/colorPrimaryFixedDim = 0x7f0300ff
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_default_height_material = 0x7f060002
com.github.barteksc.pdfviewer.test:attr/motionPath = 0x7f03030e
com.github.barteksc.pdfviewer.test:attr/endIconCheckable = 0x7f030187
com.github.barteksc.pdfviewer.test:attr/motionInterpolator = 0x7f03030d
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500a8
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_surface_container_lowest = 0x7f050168
com.github.barteksc.pdfviewer.test:dimen/abc_list_item_height_material = 0x7f060031
com.github.barteksc.pdfviewer.test:dimen/notification_subtext_size = 0x7f060310
com.github.barteksc.pdfviewer.test:attr/motionEasingStandardAccelerateInterpolator = 0x7f03030a
com.github.barteksc.pdfviewer.test:attr/autoCompleteTextViewStyle = 0x7f03003a
com.github.barteksc.pdfviewer.test:attr/motionEasingStandard = 0x7f030309
com.github.barteksc.pdfviewer.test:attr/itemTextAppearanceActive = 0x7f030239
com.github.barteksc.pdfviewer.test:layout/material_textinput_timepicker = 0x7f0b0039
com.github.barteksc.pdfviewer.test:attr/motionEasingLinearInterpolator = 0x7f030308
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_label_container_color = 0x7f0c0112
com.github.barteksc.pdfviewer.test:attr/backHandlingEnabled = 0x7f030042
com.github.barteksc.pdfviewer.test:style/CardView = 0x7f0f011f
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary20 = 0x7f0500c5
com.github.barteksc.pdfviewer.test:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f030304
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0031
com.github.barteksc.pdfviewer.test:id/action_bar_subtitle = 0x7f080035
com.github.barteksc.pdfviewer.test:style/Widget.Material3.NavigationRailView = 0x7f0f03b3
com.github.barteksc.pdfviewer.test:attr/textAppearanceBodyMedium = 0x7f0303ef
com.github.barteksc.pdfviewer.test:string/abc_menu_alt_shortcut_label = 0x7f0e0008
com.github.barteksc.pdfviewer.test:attr/motionEasingDecelerated = 0x7f030302
com.github.barteksc.pdfviewer.test:dimen/material_clock_display_width = 0x7f06021d
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f00d8
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderSelection = 0x7f0302b8
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f0f02e1
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_short3 = 0x7f09001d
com.github.barteksc.pdfviewer.test:color/material_personalized_primary_text_disable_only = 0x7f05028c
com.github.barteksc.pdfviewer.test:attr/voiceIcon = 0x7f030471
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f0020
com.github.barteksc.pdfviewer.test:attr/queryHint = 0x7f030354
com.github.barteksc.pdfviewer.test:attr/motionEasingAccelerated = 0x7f030301
com.github.barteksc.pdfviewer.test:attr/motionDurationShort1 = 0x7f0302fd
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004b
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_horizontal_edge_offset = 0x7f060244
com.github.barteksc.pdfviewer.test:attr/chipSpacing = 0x7f0300b7
com.github.barteksc.pdfviewer.test:attr/motionDurationMedium4 = 0x7f0302fc
com.github.barteksc.pdfviewer.test:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f06023f
com.github.barteksc.pdfviewer.test:id/fill_horizontal = 0x7f0800a0
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral96 = 0x7f05010b
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602ae
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_icon_margin = 0x7f0600a1
com.github.barteksc.pdfviewer.test:attr/motionDurationMedium3 = 0x7f0302fb
com.github.barteksc.pdfviewer.test:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c5
com.github.barteksc.pdfviewer.test:attr/badgeStyle = 0x7f030053
com.github.barteksc.pdfviewer.test:attr/motionDurationMedium1 = 0x7f0302f9
com.github.barteksc.pdfviewer.test:dimen/design_navigation_item_icon_padding = 0x7f060079
com.github.barteksc.pdfviewer.test:attr/motionDurationLong1 = 0x7f0302f5
com.github.barteksc.pdfviewer.test:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070034
com.github.barteksc.pdfviewer.test:styleable/MaterialAutoCompleteTextView = 0x7f10004b
com.github.barteksc.pdfviewer.test:attr/motionDurationExtraLong4 = 0x7f0302f4
com.github.barteksc.pdfviewer.test:attr/motionDurationExtraLong3 = 0x7f0302f3
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602d8
com.github.barteksc.pdfviewer.test:id/flip = 0x7f0800a9
com.github.barteksc.pdfviewer.test:dimen/mtrl_fab_elevation = 0x7f0602af
com.github.barteksc.pdfviewer.test:id/mtrl_picker_header_title_and_selection = 0x7f080105
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a0
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f0272
com.github.barteksc.pdfviewer.test:attr/mock_showLabel = 0x7f0302ef
com.github.barteksc.pdfviewer.test:attr/actionButtonStyle = 0x7f03000b
com.github.barteksc.pdfviewer.test:attr/mock_showDiagonals = 0x7f0302ee
com.github.barteksc.pdfviewer.test:string/mtrl_picker_range_header_title = 0x7f0e0081
com.github.barteksc.pdfviewer.test:attr/layout_constraintLeft_creator = 0x7f030268
com.github.barteksc.pdfviewer.test:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.github.barteksc.pdfviewer.test:attr/badgeShapeAppearance = 0x7f030051
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f0f01f0
com.github.barteksc.pdfviewer.test:attr/mock_labelBackgroundColor = 0x7f0302ec
com.github.barteksc.pdfviewer.test:attr/fontProviderAuthority = 0x7f0301e2
com.github.barteksc.pdfviewer.test:attr/mock_label = 0x7f0302eb
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_secondary_container = 0x7f05019d
com.github.barteksc.pdfviewer.test:attr/scrimAnimationDuration = 0x7f03036a
com.github.barteksc.pdfviewer.test:drawable/abc_cab_background_internal_bg = 0x7f070037
com.github.barteksc.pdfviewer.test:attr/minTouchTargetSize = 0x7f0302e8
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0502b4
com.github.barteksc.pdfviewer.test:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.github.barteksc.pdfviewer.test:drawable/notification_bg_low_pressed = 0x7f0700dc
com.github.barteksc.pdfviewer.test:attr/menuGravity = 0x7f0302e4
com.github.barteksc.pdfviewer.test:color/design_icon_tint = 0x7f050053
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500db
com.github.barteksc.pdfviewer.test:macro/m3_comp_dialog_headline_color = 0x7f0c0025
com.github.barteksc.pdfviewer.test:attr/menuAlignmentMode = 0x7f0302e3
com.github.barteksc.pdfviewer.test:attr/menu = 0x7f0302e2
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f0f0184
com.github.barteksc.pdfviewer.test:dimen/highlight_alpha_material_colored = 0x7f060093
com.github.barteksc.pdfviewer.test:attr/boxCornerRadiusTopStart = 0x7f03007d
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cf
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602d6
com.github.barteksc.pdfviewer.test:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.github.barteksc.pdfviewer.test:drawable/ic_call_answer_video = 0x7f07008d
com.github.barteksc.pdfviewer.test:attr/materialAlertDialogTitleTextStyle = 0x7f0302ad
com.github.barteksc.pdfviewer.test:color/m3_dynamic_dark_primary_text_disable_only = 0x7f05007f
com.github.barteksc.pdfviewer.test:attr/flow_verticalStyle = 0x7f0301de
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant30 = 0x7f050112
com.github.barteksc.pdfviewer.test:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f00c0
com.github.barteksc.pdfviewer.test:id/header_title = 0x7f0800b5
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_check_mark = 0x7f0700c2
com.github.barteksc.pdfviewer.test:attr/maxLines = 0x7f0302dd
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500d2
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f00e2
com.github.barteksc.pdfviewer.test:color/abc_secondary_text_material_dark = 0x7f050011
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f0f0064
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral0 = 0x7f050203
com.github.barteksc.pdfviewer.test:attr/errorAccessibilityLabel = 0x7f030192
com.github.barteksc.pdfviewer.test:attr/maxButtonHeight = 0x7f0302d9
com.github.barteksc.pdfviewer.test:attr/drawableSize = 0x7f030171
com.github.barteksc.pdfviewer.test:attr/activityChooserViewStyle = 0x7f030025
com.github.barteksc.pdfviewer.test:attr/maxWidth = 0x7f0302e0
com.github.barteksc.pdfviewer.test:color/cardview_shadow_start_color = 0x7f05002e
com.github.barteksc.pdfviewer.test:attr/listLayout = 0x7f030296
com.github.barteksc.pdfviewer.test:attr/materialTimePickerTheme = 0x7f0302d5
com.github.barteksc.pdfviewer.test:color/mtrl_chip_text_color = 0x7f0502a7
com.github.barteksc.pdfviewer.test:color/material_on_primary_disabled = 0x7f050252
com.github.barteksc.pdfviewer.test:attr/animationMode = 0x7f030032
com.github.barteksc.pdfviewer.test:attr/scrimVisibleHeightTrigger = 0x7f03036c
com.github.barteksc.pdfviewer.test:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060231
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f0f025e
com.github.barteksc.pdfviewer.test:attr/materialSwitchStyle = 0x7f0302d2
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d2
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011c
com.github.barteksc.pdfviewer.test:styleable/MaterialRadioButton = 0x7f100054
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.LargeComponent = 0x7f0f0175
com.github.barteksc.pdfviewer.test:color/design_default_color_on_error = 0x7f050041
com.github.barteksc.pdfviewer.test:attr/materialSearchViewPrefixStyle = 0x7f0302ce
com.github.barteksc.pdfviewer.test:animator/mtrl_chip_state_list_anim = 0x7f020018
com.github.barteksc.pdfviewer.test:attr/materialSearchBarStyle = 0x7f0302cd
com.github.barteksc.pdfviewer.test:attr/dayTodayStyle = 0x7f030154
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600bd
com.github.barteksc.pdfviewer.test:attr/layout_constraintRight_creator = 0x7f03026b
com.github.barteksc.pdfviewer.test:attr/actionBarTheme = 0x7f030009
com.github.barteksc.pdfviewer.test:macro/m3_comp_linear_progress_indicator_track_color = 0x7f0c005f
com.github.barteksc.pdfviewer.test:attr/materialDividerHeavyStyle = 0x7f0302c7
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f0f016e
com.github.barteksc.pdfviewer.test:attr/thumbIcon = 0x7f030422
com.github.barteksc.pdfviewer.test:attr/materialDisplayDividerStyle = 0x7f0302c6
com.github.barteksc.pdfviewer.test:attr/textAppearanceBodyLarge = 0x7f0303ee
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f0f0285
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f0f0097
com.github.barteksc.pdfviewer.test:dimen/m3_slider_inactive_track_height = 0x7f0601e3
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602bf
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error10 = 0x7f0500ea
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_container_lowest = 0x7f05027e
com.github.barteksc.pdfviewer.test:attr/materialCardViewOutlinedStyle = 0x7f0302c2
com.github.barteksc.pdfviewer.test:attr/materialCalendarStyle = 0x7f0302bd
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f0f0436
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060259
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_9 = 0x7f08002f
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_size_medium = 0x7f0600ab
com.github.barteksc.pdfviewer.test:attr/checkedIconGravity = 0x7f0300a5
com.github.barteksc.pdfviewer.test:attr/materialCalendarMonth = 0x7f0302bb
com.github.barteksc.pdfviewer.test:styleable/MaterialCalendarItem = 0x7f10004f
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500a4
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderTitle = 0x7f0302b9
com.github.barteksc.pdfviewer.test:dimen/tooltip_y_offset_non_touch = 0x7f060319
com.github.barteksc.pdfviewer.test:attr/colorPrimarySurface = 0x7f030101
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Light = 0x7f0f02cd
com.github.barteksc.pdfviewer.test:attr/prefixText = 0x7f03034c
com.github.barteksc.pdfviewer.test:animator/mtrl_card_state_list_anim = 0x7f020017
com.github.barteksc.pdfviewer.test:dimen/m3_chip_corner_size = 0x7f0600f4
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderConfirmButton = 0x7f0302b5
com.github.barteksc.pdfviewer.test:drawable/abc_switch_thumb_material = 0x7f070069
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_primary = 0x7f05015e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f0f0421
com.github.barteksc.pdfviewer.test:attr/expandedTitleGravity = 0x7f03019f
com.github.barteksc.pdfviewer.test:attr/materialCalendarDay = 0x7f0302b1
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f0f0168
com.github.barteksc.pdfviewer.test:attr/materialButtonToggleGroupStyle = 0x7f0302b0
com.github.barteksc.pdfviewer.test:attr/transitionEasing = 0x7f030461
com.github.barteksc.pdfviewer.test:attr/materialButtonOutlinedStyle = 0x7f0302ae
com.github.barteksc.pdfviewer.test:id/fitStart = 0x7f0800a5
com.github.barteksc.pdfviewer.test:attr/passwordToggleContentDescription = 0x7f030337
com.github.barteksc.pdfviewer.test:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302a9
com.github.barteksc.pdfviewer.test:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0502ba
com.github.barteksc.pdfviewer.test:attr/logoDescription = 0x7f0302a2
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_bar_ripple_color = 0x7f0502b6
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_body_1_material = 0x7f06003f
com.github.barteksc.pdfviewer.test:attr/boxCornerRadiusTopEnd = 0x7f03007c
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f0f03d8
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b8
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601f5
com.github.barteksc.pdfviewer.test:attr/actionViewClass = 0x7f030023
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700cd
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_icon_size = 0x7f06011b
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_bright = 0x7f0501cd
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f001e
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0137
com.github.barteksc.pdfviewer.test:attr/listPreferredItemPaddingStart = 0x7f03029f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.Badge = 0x7f0f03ea
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CompoundButton.Switch = 0x7f0f0378
com.github.barteksc.pdfviewer.test:attr/fabSize = 0x7f0301b7
com.github.barteksc.pdfviewer.test:anim/design_snackbar_out = 0x7f01001b
com.github.barteksc.pdfviewer.test:attr/itemShapeInsetStart = 0x7f030233
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f0f0103
com.github.barteksc.pdfviewer.test:attr/chipEndPadding = 0x7f0300ae
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500d8
com.github.barteksc.pdfviewer.test:styleable/NavigationBarActiveIndicator = 0x7f100064
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_item_icon_size = 0x7f0602c1
com.github.barteksc.pdfviewer.test:attr/listPreferredItemHeightLarge = 0x7f03029a
com.github.barteksc.pdfviewer.test:dimen/material_emphasis_high_type = 0x7f06022d
com.github.barteksc.pdfviewer.test:attr/actionModeCutDrawable = 0x7f030015
com.github.barteksc.pdfviewer.test:attr/textAppearanceTitleLarge = 0x7f03040e
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f0f0399
com.github.barteksc.pdfviewer.test:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.github.barteksc.pdfviewer.test:string/mtrl_picker_end_date_description = 0x7f0e0076
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700d0
com.github.barteksc.pdfviewer.test:attr/listChoiceIndicatorMultipleAnimated = 0x7f030292
com.github.barteksc.pdfviewer.test:attr/materialCardViewStyle = 0x7f0302c3
com.github.barteksc.pdfviewer.test:color/material_timepicker_modebutton_tint = 0x7f050297
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat = 0x7f0f020b
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.Material3.MediumComponent = 0x7f0f0176
com.github.barteksc.pdfviewer.test:attr/endIconScaleType = 0x7f03018c
com.github.barteksc.pdfviewer.test:attr/dividerColor = 0x7f030162
com.github.barteksc.pdfviewer.test:dimen/compat_control_corner_material = 0x7f06005a
com.github.barteksc.pdfviewer.test:animator/design_fab_show_motion_spec = 0x7f020002
com.github.barteksc.pdfviewer.test:color/mtrl_outlined_stroke_color = 0x7f0502bd
com.github.barteksc.pdfviewer.test:color/abc_btn_colored_text_material = 0x7f050003
com.github.barteksc.pdfviewer.test:dimen/design_navigation_max_width = 0x7f06007b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_view_container_elevation = 0x7f060171
com.github.barteksc.pdfviewer.test:attr/labelBehavior = 0x7f030242
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TonalButton = 0x7f0f035c
com.github.barteksc.pdfviewer.test:id/decelerateAndComplete = 0x7f080079
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602aa
com.github.barteksc.pdfviewer.test:string/mtrl_picker_text_input_date_range_start_hint = 0x7f0e0087
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_action_top_padding = 0x7f06009e
com.github.barteksc.pdfviewer.test:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f01b0
com.github.barteksc.pdfviewer.test:attr/motionDebug = 0x7f0302f0
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f0f0456
com.github.barteksc.pdfviewer.test:id/clockwise = 0x7f080068
com.github.barteksc.pdfviewer.test:attr/chipGroupStyle = 0x7f0300af
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012d
com.github.barteksc.pdfviewer.test:attr/layout_goneMarginTop = 0x7f030283
com.github.barteksc.pdfviewer.test:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f0f00a0
com.github.barteksc.pdfviewer.test:attr/flow_padding = 0x7f0301da
com.github.barteksc.pdfviewer.test:id/dropdown_menu = 0x7f08008f
com.github.barteksc.pdfviewer.test:attr/layout_goneMarginRight = 0x7f030281
com.github.barteksc.pdfviewer.test:attr/tickVisible = 0x7f030434
com.github.barteksc.pdfviewer.test:attr/layout_goneMarginBottom = 0x7f03027e
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_text_btn_padding_right = 0x7f060267
com.github.barteksc.pdfviewer.test:attr/layout_constraintWidth_percent = 0x7f03027a
com.github.barteksc.pdfviewer.test:color/abc_search_url_text_selected = 0x7f050010
com.github.barteksc.pdfviewer.test:layout/notification_template_custom_big = 0x7f0b0061
com.github.barteksc.pdfviewer.test:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005d
com.github.barteksc.pdfviewer.test:dimen/m3_carousel_extra_small_item_size = 0x7f0600ee
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500e0
com.github.barteksc.pdfviewer.test:macro/m3_comp_input_chip_container_shape = 0x7f0c005c
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501ab
com.github.barteksc.pdfviewer.test:attr/drawableStartCompat = 0x7f030172
com.github.barteksc.pdfviewer.test:color/m3_chip_stroke_color = 0x7f050072
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_primary_container = 0x7f05015f
com.github.barteksc.pdfviewer.test:attr/layout_constraintTop_creator = 0x7f030271
com.github.barteksc.pdfviewer.test:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601e7
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f00e0
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_min_width = 0x7f060084
com.github.barteksc.pdfviewer.test:attr/layout_constraintTag = 0x7f030270
com.github.barteksc.pdfviewer.test:attr/listPreferredItemPaddingEnd = 0x7f03029c
com.github.barteksc.pdfviewer.test:dimen/m3_sys_elevation_level3 = 0x7f0601ec
com.github.barteksc.pdfviewer.test:attr/layout_constraintRight_toLeftOf = 0x7f03026c
com.github.barteksc.pdfviewer.test:id/labeled = 0x7f0800c8
com.github.barteksc.pdfviewer.test:attr/layout_constraintLeft_toRightOf = 0x7f03026a
com.github.barteksc.pdfviewer.test:dimen/notification_action_icon_size = 0x7f060304
com.github.barteksc.pdfviewer.test:attr/paddingBottomSystemWindowInsets = 0x7f03032c
com.github.barteksc.pdfviewer.test:animator/design_appbar_state_list_animator = 0x7f020000
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_track_height = 0x7f0602e5
com.github.barteksc.pdfviewer.test:string/abc_searchview_description_clear = 0x7f0e0013
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00ac
com.github.barteksc.pdfviewer.test:attr/motionEasingLinear = 0x7f030307
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Menu = 0x7f0f01a6
com.github.barteksc.pdfviewer.test:color/call_notification_decline_color = 0x7f05002a
com.github.barteksc.pdfviewer.test:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601b5
com.github.barteksc.pdfviewer.test:attr/icon = 0x7f030209
com.github.barteksc.pdfviewer.test:attr/maxHeight = 0x7f0302db
com.github.barteksc.pdfviewer.test:string/material_hour_24h_suffix = 0x7f0e0048
com.github.barteksc.pdfviewer.test:attr/font = 0x7f0301e0
com.github.barteksc.pdfviewer.test:attr/layout_constraintHorizontal_chainStyle = 0x7f030266
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DynamicColors.DayNight = 0x7f0f0238
com.github.barteksc.pdfviewer.test:attr/motionDurationExtraLong1 = 0x7f0302f1
com.github.barteksc.pdfviewer.test:attr/hideOnContentScroll = 0x7f0301fe
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary95 = 0x7f050140
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602c8
com.github.barteksc.pdfviewer.test:attr/cornerSizeTopRight = 0x7f03013c
com.github.barteksc.pdfviewer.test:attr/commitIcon = 0x7f030117
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_path = 0x7f09001f
com.github.barteksc.pdfviewer.test:color/ripple_material_light = 0x7f0502da
com.github.barteksc.pdfviewer.test:attr/dropDownBackgroundTint = 0x7f030179
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ActionMode = 0x7f0f033c
com.github.barteksc.pdfviewer.test:attr/layout_constraintHorizontal_bias = 0x7f030265
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060201
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f060195
com.github.barteksc.pdfviewer.test:attr/materialIconButtonOutlinedStyle = 0x7f0302cb
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Caption = 0x7f0f01fb
com.github.barteksc.pdfviewer.test:attr/layout_constraintHeight_percent = 0x7f030264
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Dark = 0x7f0f0089
com.github.barteksc.pdfviewer.test:attr/startIconDrawable = 0x7f0303a4
com.github.barteksc.pdfviewer.test:attr/sideSheetDialogTheme = 0x7f03038c
com.github.barteksc.pdfviewer.test:attr/errorAccessibilityLiveRegion = 0x7f030193
com.github.barteksc.pdfviewer.test:attr/layout_constraintHeight_min = 0x7f030263
com.github.barteksc.pdfviewer.test:attr/dragDirection = 0x7f030169
com.github.barteksc.pdfviewer.test:attr/chipSpacingHorizontal = 0x7f0300b8
com.github.barteksc.pdfviewer.test:attr/layout_constraintHeight_max = 0x7f030262
com.github.barteksc.pdfviewer.test:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.github.barteksc.pdfviewer.test:attr/fontWeight = 0x7f0301eb
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Button.IconButton = 0x7f0f0288
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c007b
com.github.barteksc.pdfviewer.test:attr/contentDescription = 0x7f03011f
com.github.barteksc.pdfviewer.test:dimen/mtrl_progress_circular_size_medium = 0x7f0602d4
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonPrimaryStyle = 0x7f0301c3
com.github.barteksc.pdfviewer.test:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f00be
com.github.barteksc.pdfviewer.test:attr/layout_constraintEnd_toEndOf = 0x7f03025c
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_container_low = 0x7f0501d1
com.github.barteksc.pdfviewer.test:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007a
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_caption_material = 0x7f060042
com.github.barteksc.pdfviewer.test:id/disjoint = 0x7f080088
com.github.barteksc.pdfviewer.test:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602b5
com.github.barteksc.pdfviewer.test:layout/mtrl_alert_dialog = 0x7f0b003f
com.github.barteksc.pdfviewer.test:color/m3_elevated_chip_background_color = 0x7f050086
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_active_item_min_width = 0x7f060060
com.github.barteksc.pdfviewer.test:attr/layout_constraintCircle = 0x7f030258
com.github.barteksc.pdfviewer.test:attr/checkMarkTintMode = 0x7f03009f
com.github.barteksc.pdfviewer.test:dimen/tooltip_vertical_padding = 0x7f060318
com.github.barteksc.pdfviewer.test:color/notification_action_color_filter = 0x7f0502cf
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_text_input_date_range = 0x7f0b005c
com.github.barteksc.pdfviewer.test:attr/layout_constrainedHeight = 0x7f030251
com.github.barteksc.pdfviewer.test:attr/contentPaddingStart = 0x7f03012b
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f06027e
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060210
com.github.barteksc.pdfviewer.test:attr/fabCradleMargin = 0x7f0301b3
com.github.barteksc.pdfviewer.test:attr/cursorColor = 0x7f030145
com.github.barteksc.pdfviewer.test:attr/flow_maxElementsWrap = 0x7f0301d9
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Dark = 0x7f0f0059
com.github.barteksc.pdfviewer.test:color/mtrl_tabs_colored_ripple_color = 0x7f0502c4
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500e2
com.github.barteksc.pdfviewer.test:attr/preserveIconSpacing = 0x7f03034f
com.github.barteksc.pdfviewer.test:color/abc_search_url_text = 0x7f05000d
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f0f0125
com.github.barteksc.pdfviewer.test:id/tag_screen_reader_focusable = 0x7f080188
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_4 = 0x7f08002a
com.github.barteksc.pdfviewer.test:color/m3_dynamic_primary_text_disable_only = 0x7f050084
com.github.barteksc.pdfviewer.test:attr/checkedIconSize = 0x7f0300a7
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_day_width = 0x7f060274
com.github.barteksc.pdfviewer.test:attr/keyPositionType = 0x7f03023e
com.github.barteksc.pdfviewer.test:drawable/mtrl_dialog_background = 0x7f0700bd
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500a5
com.github.barteksc.pdfviewer.test:attr/listItemLayout = 0x7f030295
com.github.barteksc.pdfviewer.test:attr/badgeWithTextShapeAppearanceOverlay = 0x7f03005d
com.github.barteksc.pdfviewer.test:color/m3_checkbox_button_icon_tint = 0x7f05006d
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f0307
com.github.barteksc.pdfviewer.test:attr/selectableItemBackgroundBorderless = 0x7f030373
com.github.barteksc.pdfviewer.test:attr/toolbarNavigationButtonStyle = 0x7f030449
com.github.barteksc.pdfviewer.test:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.github.barteksc.pdfviewer.test:attr/itemSpacing = 0x7f030235
com.github.barteksc.pdfviewer.test:attr/colorOnBackground = 0x7f0300e4
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f0f0248
com.github.barteksc.pdfviewer.test:attr/itemShapeInsetEnd = 0x7f030232
com.github.barteksc.pdfviewer.test:attr/itemShapeInsetBottom = 0x7f030231
com.github.barteksc.pdfviewer.test:attr/fastScrollHorizontalTrackDrawable = 0x7f0301ba
com.github.barteksc.pdfviewer.test:dimen/abc_switch_padding = 0x7f06003e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f0f0434
com.github.barteksc.pdfviewer.test:id/visible = 0x7f0801b8
com.github.barteksc.pdfviewer.test:attr/itemShapeAppearance = 0x7f03022e
com.github.barteksc.pdfviewer.test:dimen/m3_large_fab_max_image_size = 0x7f0601b3
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013f
com.github.barteksc.pdfviewer.test:color/mtrl_chip_background_color = 0x7f0502a4
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700d3
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c0080
com.github.barteksc.pdfviewer.test:id/reverseSawtooth = 0x7f080142
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_background = 0x7f05018f
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f00d5
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0095
com.github.barteksc.pdfviewer.test:attr/itemPadding = 0x7f03022a
com.github.barteksc.pdfviewer.test:dimen/m3_ripple_default_alpha = 0x7f0601cd
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0314
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011b
com.github.barteksc.pdfviewer.test:attr/actionBarDivider = 0x7f030000
com.github.barteksc.pdfviewer.test:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f06019f
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f0f03e5
com.github.barteksc.pdfviewer.test:attr/itemMinHeight = 0x7f030229
com.github.barteksc.pdfviewer.test:id/endToStart = 0x7f080098
com.github.barteksc.pdfviewer.test:attr/chipSpacingVertical = 0x7f0300b9
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_primary_dark = 0x7f050039
com.github.barteksc.pdfviewer.test:dimen/m3_carousel_small_item_size_min = 0x7f0600f2
com.github.barteksc.pdfviewer.test:attr/colorOnSecondaryFixedVariant = 0x7f0300f1
com.github.barteksc.pdfviewer.test:attr/itemHorizontalTranslationEnabled = 0x7f030224
com.github.barteksc.pdfviewer.test:attr/isMaterialTheme = 0x7f03021f
com.github.barteksc.pdfviewer.test:attr/cardViewStyle = 0x7f03009a
com.github.barteksc.pdfviewer.test:attr/isMaterial3Theme = 0x7f03021e
com.github.barteksc.pdfviewer.test:id/BOTTOM_START = 0x7f080002
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary20 = 0x7f050138
com.github.barteksc.pdfviewer.test:drawable/abc_btn_colored_material = 0x7f07002f
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_padding_bottom = 0x7f06025e
com.github.barteksc.pdfviewer.test:attr/actionBarWidgetTheme = 0x7f03000a
com.github.barteksc.pdfviewer.test:attr/indicatorDirectionLinear = 0x7f030217
com.github.barteksc.pdfviewer.test:attr/cornerFamilyBottomLeft = 0x7f030133
com.github.barteksc.pdfviewer.test:attr/singleLine = 0x7f030393
com.github.barteksc.pdfviewer.test:dimen/material_textinput_max_width = 0x7f06023a
com.github.barteksc.pdfviewer.test:attr/behavior_peekHeight = 0x7f03006b
com.github.barteksc.pdfviewer.test:attr/closeIconEndPadding = 0x7f0300c9
com.github.barteksc.pdfviewer.test:attr/colorOnTertiaryFixedVariant = 0x7f0300f8
com.github.barteksc.pdfviewer.test:id/centerCrop = 0x7f08005b
com.github.barteksc.pdfviewer.test:color/material_divider_color = 0x7f050202
com.github.barteksc.pdfviewer.test:attr/indicatorDirectionCircular = 0x7f030216
com.github.barteksc.pdfviewer.test:attr/chipBackgroundColor = 0x7f0300ac
com.github.barteksc.pdfviewer.test:attr/imageButtonStyle = 0x7f030212
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_medium1 = 0x7f090017
com.github.barteksc.pdfviewer.test:id/chain = 0x7f08005f
com.github.barteksc.pdfviewer.test:attr/layout_constraintHeight_default = 0x7f030261
com.github.barteksc.pdfviewer.test:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cc
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary95 = 0x7f050126
com.github.barteksc.pdfviewer.test:attr/framePosition = 0x7f0301ef
com.github.barteksc.pdfviewer.test:attr/duration = 0x7f03017c
com.github.barteksc.pdfviewer.test:attr/iconTintMode = 0x7f030210
com.github.barteksc.pdfviewer.test:attr/cornerFamily = 0x7f030132
com.github.barteksc.pdfviewer.test:attr/snackbarButtonStyle = 0x7f030397
com.github.barteksc.pdfviewer.test:dimen/fastscroll_default_thickness = 0x7f060090
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Body2 = 0x7f0f0195
com.github.barteksc.pdfviewer.test:attr/fabCustomSize = 0x7f0301b6
com.github.barteksc.pdfviewer.test:attr/materialSearchViewToolbarHeight = 0x7f0302d0
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c2
com.github.barteksc.pdfviewer.test:attr/drawableTintMode = 0x7f030174
com.github.barteksc.pdfviewer.test:attr/iconStartPadding = 0x7f03020e
com.github.barteksc.pdfviewer.test:attr/itemIconPadding = 0x7f030225
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_primary = 0x7f050154
com.github.barteksc.pdfviewer.test:dimen/abc_config_prefDialogWidth = 0x7f060017
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f0f0083
com.github.barteksc.pdfviewer.test:attr/saturation = 0x7f030365
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f0f0074
com.github.barteksc.pdfviewer.test:attr/horizontalOffsetWithText = 0x7f030207
com.github.barteksc.pdfviewer.test:attr/textAppearanceBodySmall = 0x7f0303f0
com.github.barteksc.pdfviewer.test:attr/layout_editor_absoluteY = 0x7f03027d
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f0f0189
com.github.barteksc.pdfviewer.test:attr/boxCornerRadiusBottomStart = 0x7f03007b
com.github.barteksc.pdfviewer.test:attr/homeLayout = 0x7f030205
com.github.barteksc.pdfviewer.test:attr/textAppearanceLineHeightEnabled = 0x7f030403
com.github.barteksc.pdfviewer.test:id/NO_DEBUG = 0x7f080006
com.github.barteksc.pdfviewer.test:string/mtrl_picker_start_date_description = 0x7f0e0084
com.github.barteksc.pdfviewer.test:color/m3_calendar_item_disabled_text = 0x7f050068
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b2
com.github.barteksc.pdfviewer.test:drawable/material_ic_clear_black_24dp = 0x7f0700aa
com.github.barteksc.pdfviewer.test:attr/telltales_tailColor = 0x7f0303e8
com.github.barteksc.pdfviewer.test:attr/srcCompat = 0x7f03039f
com.github.barteksc.pdfviewer.test:attr/isMaterial3DynamicColorApplied = 0x7f03021d
com.github.barteksc.pdfviewer.test:animator/mtrl_btn_state_list_anim = 0x7f020015
com.github.barteksc.pdfviewer.test:attr/errorContentDescription = 0x7f030194
com.github.barteksc.pdfviewer.test:attr/layout_constraintGuide_percent = 0x7f030260
com.github.barteksc.pdfviewer.test:style/Theme.Design.BottomSheetDialog = 0x7f0f0222
com.github.barteksc.pdfviewer.test:attr/goIcon = 0x7f0301f2
com.github.barteksc.pdfviewer.test:attr/layout_scrollInterpolator = 0x7f030289
com.github.barteksc.pdfviewer.test:drawable/material_cursor_drawable = 0x7f0700a8
com.github.barteksc.pdfviewer.test:id/scroll = 0x7f08014e
com.github.barteksc.pdfviewer.test:color/mtrl_navigation_bar_colored_item_tint = 0x7f0502b3
com.github.barteksc.pdfviewer.test:attr/itemTextAppearance = 0x7f030238
com.github.barteksc.pdfviewer.test:attr/gapBetweenBars = 0x7f0301f0
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Chip.Assist = 0x7f0f028f
com.github.barteksc.pdfviewer.test:attr/queryBackground = 0x7f030353
com.github.barteksc.pdfviewer.test:attr/fontProviderSystemFontFamily = 0x7f0301e8
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_small_container_height = 0x7f060120
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary100 = 0x7f05012a
com.github.barteksc.pdfviewer.test:attr/titleTextEllipsize = 0x7f030445
com.github.barteksc.pdfviewer.test:attr/contentPaddingTop = 0x7f03012c
com.github.barteksc.pdfviewer.test:attr/fontProviderFetchTimeout = 0x7f0301e5
com.github.barteksc.pdfviewer.test:attr/boxStrokeErrorColor = 0x7f03007f
com.github.barteksc.pdfviewer.test:id/action_container = 0x7f080037
com.github.barteksc.pdfviewer.test:id/search_bar = 0x7f080154
com.github.barteksc.pdfviewer.test:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f060232
com.github.barteksc.pdfviewer.test:attr/fontFamily = 0x7f0301e1
com.github.barteksc.pdfviewer.test:color/design_bottom_navigation_shadow_color = 0x7f05002f
com.github.barteksc.pdfviewer.test:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.github.barteksc.pdfviewer.test:attr/tabPaddingStart = 0x7f0303dd
com.github.barteksc.pdfviewer.test:attr/constraintSetStart = 0x7f03011b
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_margin = 0x7f060068
com.github.barteksc.pdfviewer.test:attr/layoutManager = 0x7f03024b
com.github.barteksc.pdfviewer.test:color/material_personalized_color_secondary_container = 0x7f050275
com.github.barteksc.pdfviewer.test:id/scale = 0x7f08014c
com.github.barteksc.pdfviewer.test:attr/flow_verticalGap = 0x7f0301dd
com.github.barteksc.pdfviewer.test:attr/flow_verticalAlign = 0x7f0301db
com.github.barteksc.pdfviewer.test:attr/layout_constraintBottom_toTopOf = 0x7f030257
com.github.barteksc.pdfviewer.test:id/wrap = 0x7f0801bd
com.github.barteksc.pdfviewer.test:attr/searchViewStyle = 0x7f030370
com.github.barteksc.pdfviewer.test:attr/colorOnErrorContainer = 0x7f0300e8
com.github.barteksc.pdfviewer.test:attr/flow_wrapMode = 0x7f0301df
com.github.barteksc.pdfviewer.test:styleable/AnimatedStateListDrawableCompat = 0x7f100007
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f01be
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_surface_container = 0x7f0501ce
com.github.barteksc.pdfviewer.test:attr/hintTextColor = 0x7f030203
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060166
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f01b6
com.github.barteksc.pdfviewer.test:attr/colorPrimaryVariant = 0x7f030102
com.github.barteksc.pdfviewer.test:attr/flow_lastVerticalStyle = 0x7f0301d8
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f060130
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Light.Bridge = 0x7f0f0261
com.github.barteksc.pdfviewer.test:string/mtrl_picker_a11y_next_month = 0x7f0e006b
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0139
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500b6
com.github.barteksc.pdfviewer.test:drawable/ic_keyboard_black_24dp = 0x7f070093
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_day_today_stroke = 0x7f060272
com.github.barteksc.pdfviewer.test:attr/flow_lastVerticalBias = 0x7f0301d7
com.github.barteksc.pdfviewer.test:dimen/m3_fab_border_width = 0x7f0601af
com.github.barteksc.pdfviewer.test:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f0f008a
com.github.barteksc.pdfviewer.test:attr/flow_lastHorizontalBias = 0x7f0301d5
com.github.barteksc.pdfviewer.test:dimen/mtrl_badge_size = 0x7f060246
com.github.barteksc.pdfviewer.test:attr/helperText = 0x7f0301f7
com.github.barteksc.pdfviewer.test:styleable/RecyclerView = 0x7f100070
com.github.barteksc.pdfviewer.test:attr/materialCalendarFullscreenTheme = 0x7f0302b3
com.github.barteksc.pdfviewer.test:color/m3_dark_hint_foreground = 0x7f050077
com.github.barteksc.pdfviewer.test:attr/flow_horizontalStyle = 0x7f0301d4
com.github.barteksc.pdfviewer.test:dimen/m3_ripple_focused_alpha = 0x7f0601ce
com.github.barteksc.pdfviewer.test:color/tooltip_background_dark = 0x7f0502e5
com.github.barteksc.pdfviewer.test:attr/labelVisibilityMode = 0x7f030244
com.github.barteksc.pdfviewer.test:string/side_sheet_behavior = 0x7f0e00a4
com.github.barteksc.pdfviewer.test:attr/flow_horizontalBias = 0x7f0301d2
com.github.barteksc.pdfviewer.test:attr/chipSurfaceColor = 0x7f0300bf
com.github.barteksc.pdfviewer.test:attr/flow_horizontalAlign = 0x7f0301d1
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_inverse = 0x7f050280
com.github.barteksc.pdfviewer.test:dimen/mtrl_card_elevation = 0x7f060298
com.github.barteksc.pdfviewer.test:dimen/m3_card_elevation = 0x7f0600ea
com.github.barteksc.pdfviewer.test:style/Widget.Design.FloatingActionButton = 0x7f0f0334
com.github.barteksc.pdfviewer.test:color/mtrl_choice_chip_text_color = 0x7f0502aa
com.github.barteksc.pdfviewer.test:layout/abc_action_menu_item_layout = 0x7f0b0002
com.github.barteksc.pdfviewer.test:dimen/design_bottom_navigation_active_item_max_width = 0x7f06005f
com.github.barteksc.pdfviewer.test:color/material_personalized_color_outline = 0x7f05026d
com.github.barteksc.pdfviewer.test:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.github.barteksc.pdfviewer.test:attr/flow_firstVerticalBias = 0x7f0301cf
com.github.barteksc.pdfviewer.test:attr/chipIconTint = 0x7f0300b3
com.github.barteksc.pdfviewer.test:attr/marginHorizontal = 0x7f0302a4
com.github.barteksc.pdfviewer.test:attr/customBoolean = 0x7f030148
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f0f015c
com.github.barteksc.pdfviewer.test:id/material_clock_level = 0x7f0800da
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301c9
com.github.barteksc.pdfviewer.test:id/cancel_button = 0x7f080059
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSmallStyle = 0x7f0301c7
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0158
com.github.barteksc.pdfviewer.test:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060114
com.github.barteksc.pdfviewer.test:styleable/KeyFramesAcceleration = 0x7f10003f
com.github.barteksc.pdfviewer.test:attr/motionDurationLong2 = 0x7f0302f6
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f0f0393
com.github.barteksc.pdfviewer.test:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602b9
com.github.barteksc.pdfviewer.test:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601a2
com.github.barteksc.pdfviewer.test:styleable/ClockHandView = 0x7f100020
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501a4
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301be
com.github.barteksc.pdfviewer.test:animator/fragment_close_exit = 0x7f020004
com.github.barteksc.pdfviewer.test:attr/linearProgressIndicatorStyle = 0x7f030290
com.github.barteksc.pdfviewer.test:color/m3_slider_active_track_color = 0x7f050145
com.github.barteksc.pdfviewer.test:attr/fastScrollEnabled = 0x7f0301b8
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f007c
com.github.barteksc.pdfviewer.test:attr/layout_constraintCircleRadius = 0x7f03025a
com.github.barteksc.pdfviewer.test:string/m3_sys_motion_easing_standard = 0x7f0e0043
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050175
com.github.barteksc.pdfviewer.test:attr/onCross = 0x7f030323
com.github.barteksc.pdfviewer.test:color/material_personalized_color_background = 0x7f05025b
com.github.barteksc.pdfviewer.test:attr/passwordToggleTint = 0x7f03033a
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f01a5
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060128
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070018
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f02fb
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_inverse_surface = 0x7f050150
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral70 = 0x7f050104
com.github.barteksc.pdfviewer.test:color/primary_text_disabled_material_light = 0x7f0502d8
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary50 = 0x7f05023d
com.github.barteksc.pdfviewer.test:color/m3_tabs_ripple_color_secondary = 0x7f0501e6
com.github.barteksc.pdfviewer.test:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c6
com.github.barteksc.pdfviewer.test:attr/fabAlignmentModeEndMargin = 0x7f0301b0
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_min_width_major = 0x7f060022
com.github.barteksc.pdfviewer.test:attr/crossfade = 0x7f030143
com.github.barteksc.pdfviewer.test:attr/chipMinHeight = 0x7f0300b5
com.github.barteksc.pdfviewer.test:attr/autoSizeStepGranularity = 0x7f03003f
com.github.barteksc.pdfviewer.test:attr/listChoiceIndicatorSingleAnimated = 0x7f030293
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary70 = 0x7f0500ca
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_extra_long3 = 0x7f090011
com.github.barteksc.pdfviewer.test:id/mtrl_child_content_container = 0x7f0800ff
com.github.barteksc.pdfviewer.test:attr/extendedFloatingActionButtonStyle = 0x7f0301ab
com.github.barteksc.pdfviewer.test:attr/extendStrategy = 0x7f0301a8
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060158
com.github.barteksc.pdfviewer.test:attr/cornerSizeBottomLeft = 0x7f030139
com.github.barteksc.pdfviewer.test:attr/badgeWithTextShapeAppearance = 0x7f03005c
com.github.barteksc.pdfviewer.test:attr/expandedTitleTextAppearance = 0x7f0301a5
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceOverlay = 0x7f03037e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f0f0407
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0300
com.github.barteksc.pdfviewer.test:drawable/abc_text_select_handle_right_mtrl = 0x7f070070
com.github.barteksc.pdfviewer.test:attr/pressedTranslationZ = 0x7f030350
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f0f02b7
com.github.barteksc.pdfviewer.test:attr/expandedTitleMarginBottom = 0x7f0301a1
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_header_title_text = 0x7f0b0059
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary10 = 0x7f05011c
com.github.barteksc.pdfviewer.test:attr/expanded = 0x7f03019d
com.github.barteksc.pdfviewer.test:drawable/notify_panel_notification_icon_bg = 0x7f0700e3
com.github.barteksc.pdfviewer.test:attr/bottomNavigationStyle = 0x7f030073
com.github.barteksc.pdfviewer.test:attr/expandActivityOverflowButtonDrawable = 0x7f03019c
com.github.barteksc.pdfviewer.test:color/m3_dynamic_dark_default_color_primary_text = 0x7f05007b
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008e
com.github.barteksc.pdfviewer.test:color/androidx_core_ripple_material_light = 0x7f05001b
com.github.barteksc.pdfviewer.test:attr/errorEnabled = 0x7f030195
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601f4
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant10 = 0x7f05010f
com.github.barteksc.pdfviewer.test:attr/hideMotionSpec = 0x7f0301fc
com.github.barteksc.pdfviewer.test:dimen/mtrl_tooltip_minWidth = 0x7f060301
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f0f0205
com.github.barteksc.pdfviewer.test:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700d2
com.github.barteksc.pdfviewer.test:attr/colorOnPrimaryFixed = 0x7f0300eb
com.github.barteksc.pdfviewer.test:attr/cardPreventCornerOverlap = 0x7f030098
com.github.barteksc.pdfviewer.test:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.github.barteksc.pdfviewer.test:style/TextAppearance.Compat.Notification = 0x7f0f01c3
com.github.barteksc.pdfviewer.test:attr/layout_constraintWidth_max = 0x7f030278
com.github.barteksc.pdfviewer.test:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060181
com.github.barteksc.pdfviewer.test:animator/fragment_open_exit = 0x7f020008
com.github.barteksc.pdfviewer.test:attr/logoScaleType = 0x7f0302a3
com.github.barteksc.pdfviewer.test:dimen/material_textinput_default_width = 0x7f060239
com.github.barteksc.pdfviewer.test:id/design_menu_item_text = 0x7f080080
com.github.barteksc.pdfviewer.test:anim/abc_tooltip_enter = 0x7f01000a
com.github.barteksc.pdfviewer.test:attr/ensureMinTouchTargetSize = 0x7f030191
com.github.barteksc.pdfviewer.test:color/m3_timepicker_display_background_color = 0x7f0501f5
com.github.barteksc.pdfviewer.test:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f0154
com.github.barteksc.pdfviewer.test:attr/currentState = 0x7f030144
com.github.barteksc.pdfviewer.test:id/on = 0x7f08011c
com.github.barteksc.pdfviewer.test:attr/showTitle = 0x7f03038a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.OutlinedButton = 0x7f0f0354
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0154
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral50 = 0x7f050101
com.github.barteksc.pdfviewer.test:attr/buttonBarPositiveButtonStyle = 0x7f030086
com.github.barteksc.pdfviewer.test:attr/enforceMaterialTheme = 0x7f03018f
com.github.barteksc.pdfviewer.test:id/parent_matrix = 0x7f080132
com.github.barteksc.pdfviewer.test:drawable/btn_checkbox_checked_mtrl = 0x7f070079
com.github.barteksc.pdfviewer.test:dimen/design_navigation_padding_bottom = 0x7f06007c
com.github.barteksc.pdfviewer.test:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary80 = 0x7f050131
com.github.barteksc.pdfviewer.test:layout/select_dialog_multichoice_material = 0x7f0b0066
com.github.barteksc.pdfviewer.test:attr/endIconMode = 0x7f03018b
com.github.barteksc.pdfviewer.test:styleable/AppCompatTextView = 0x7f100011
com.github.barteksc.pdfviewer.test:id/rightToLeft = 0x7f080144
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f0601ff
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_outline = 0x7f05015c
com.github.barteksc.pdfviewer.test:attr/endIconDrawable = 0x7f030189
com.github.barteksc.pdfviewer.test:attr/endIconContentDescription = 0x7f030188
com.github.barteksc.pdfviewer.test:attr/layout_anchor = 0x7f03024c
com.github.barteksc.pdfviewer.test:dimen/mtrl_switch_thumb_size = 0x7f0602f1
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f0f038b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral40 = 0x7f050100
com.github.barteksc.pdfviewer.test:color/background_material_light = 0x7f050020
com.github.barteksc.pdfviewer.test:color/bright_foreground_disabled_material_light = 0x7f050022
com.github.barteksc.pdfviewer.test:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.github.barteksc.pdfviewer.test:attr/lineHeight = 0x7f03028e
com.github.barteksc.pdfviewer.test:attr/layout_constraintCircleAngle = 0x7f030259
com.github.barteksc.pdfviewer.test:id/fullscreen_header = 0x7f0800ad
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f0f0447
com.github.barteksc.pdfviewer.test:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060167
com.github.barteksc.pdfviewer.test:integer/mtrl_view_gone = 0x7f09003f
com.github.barteksc.pdfviewer.test:attr/haloRadius = 0x7f0301f4
com.github.barteksc.pdfviewer.test:attr/counterTextAppearance = 0x7f030141
com.github.barteksc.pdfviewer.test:attr/selectionRequired = 0x7f030374
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500bb
com.github.barteksc.pdfviewer.test:color/mtrl_switch_thumb_icon_tint = 0x7f0502c0
com.github.barteksc.pdfviewer.test:string/abc_menu_delete_shortcut_label = 0x7f0e000a
com.github.barteksc.pdfviewer.test:attr/counterTextColor = 0x7f030142
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary10 = 0x7f05021e
com.github.barteksc.pdfviewer.test:attr/badgeWithTextWidth = 0x7f03005e
com.github.barteksc.pdfviewer.test:drawable/mtrl_navigation_bar_item_background = 0x7f0700c7
com.github.barteksc.pdfviewer.test:attr/daySelectedStyle = 0x7f030152
com.github.barteksc.pdfviewer.test:drawable/design_password_eye = 0x7f070088
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001d
com.github.barteksc.pdfviewer.test:color/material_grey_800 = 0x7f050248
com.github.barteksc.pdfviewer.test:attr/marginRightSystemWindowInsets = 0x7f0302a6
com.github.barteksc.pdfviewer.test:attr/editTextColor = 0x7f03017f
com.github.barteksc.pdfviewer.test:attr/collapsedTitleTextColor = 0x7f0300d4
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_action_padding = 0x7f06026c
com.github.barteksc.pdfviewer.test:dimen/m3_card_disabled_z = 0x7f0600e4
com.github.barteksc.pdfviewer.test:style/Platform.MaterialComponents.Dialog = 0x7f0f0139
com.github.barteksc.pdfviewer.test:color/material_personalized_color_secondary_text_inverse = 0x7f050277
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_secondary = 0x7f050192
com.github.barteksc.pdfviewer.test:attr/endIconMinSize = 0x7f03018a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Display = 0x7f0f03ad
com.github.barteksc.pdfviewer.test:attr/drawerArrowStyle = 0x7f030176
com.github.barteksc.pdfviewer.test:attr/transitionDisable = 0x7f030460
com.github.barteksc.pdfviewer.test:attr/drawableTopCompat = 0x7f030175
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004d
com.github.barteksc.pdfviewer.test:attr/listPreferredItemPaddingRight = 0x7f03029e
com.github.barteksc.pdfviewer.test:attr/customNavigationLayout = 0x7f03014e
com.github.barteksc.pdfviewer.test:attr/maxNumber = 0x7f0302de
com.github.barteksc.pdfviewer.test:id/action_bar_root = 0x7f080033
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_primary = 0x7f0501c8
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary20 = 0x7f05011e
com.github.barteksc.pdfviewer.test:integer/mtrl_card_anim_duration_ms = 0x7f090034
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary100 = 0x7f050239
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_24 = 0x7f080021
com.github.barteksc.pdfviewer.test:attr/drawableEndCompat = 0x7f03016e
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f00f9
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_white = 0x7f050142
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301c8
com.github.barteksc.pdfviewer.test:attr/tabPaddingEnd = 0x7f0303dc
com.github.barteksc.pdfviewer.test:attr/itemTextAppearanceActiveBoldEnabled = 0x7f03023a
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_16 = 0x7f080018
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301bf
com.github.barteksc.pdfviewer.test:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010f
com.github.barteksc.pdfviewer.test:attr/autoSizeMinTextSize = 0x7f03003d
com.github.barteksc.pdfviewer.test:id/image = 0x7f0800bf
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral100 = 0x7f05009f
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.SeekBar = 0x7f0f00f4
com.github.barteksc.pdfviewer.test:id/design_menu_item_action_area = 0x7f08007e
com.github.barteksc.pdfviewer.test:color/material_slider_active_tick_marks_color = 0x7f05028d
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral10 = 0x7f050204
com.github.barteksc.pdfviewer.test:attr/badgeWithTextHeight = 0x7f03005a
com.github.barteksc.pdfviewer.test:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014e
com.github.barteksc.pdfviewer.test:attr/divider = 0x7f030161
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0142
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050170
com.github.barteksc.pdfviewer.test:attr/displayOptions = 0x7f030160
com.github.barteksc.pdfviewer.test:id/layout = 0x7f0800c9
com.github.barteksc.pdfviewer.test:color/m3_dynamic_dark_highlighted_text = 0x7f05007d
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary95 = 0x7f050235
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_22 = 0x7f08001f
com.github.barteksc.pdfviewer.test:attr/deriveConstraintsFrom = 0x7f03015c
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0094
com.github.barteksc.pdfviewer.test:dimen/mtrl_tooltip_arrowSize = 0x7f0602fe
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501b0
com.github.barteksc.pdfviewer.test:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006c
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c9
com.github.barteksc.pdfviewer.test:attr/defaultQueryHint = 0x7f030157
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_2 = 0x7f08001c
com.github.barteksc.pdfviewer.test:attr/dividerPadding = 0x7f030166
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f0f02df
com.github.barteksc.pdfviewer.test:color/material_grey_50 = 0x7f050246
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonLargeStyle = 0x7f0301c0
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_outline_variant = 0x7f050199
com.github.barteksc.pdfviewer.test:attr/fontVariationSettings = 0x7f0301ea
com.github.barteksc.pdfviewer.test:styleable/KeyCycle = 0x7f10003d
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral24 = 0x7f0500fd
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f0f0449
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_month_navigation = 0x7f0b004c
com.github.barteksc.pdfviewer.test:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_arrow_drop_down = 0x7f0700bf
com.github.barteksc.pdfviewer.test:attr/titleMarginTop = 0x7f030440
com.github.barteksc.pdfviewer.test:attr/dayInvalidStyle = 0x7f030151
com.github.barteksc.pdfviewer.test:attr/drawableRightCompat = 0x7f030170
com.github.barteksc.pdfviewer.test:attr/customPixelDimension = 0x7f03014f
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070023
com.github.barteksc.pdfviewer.test:style/MaterialAlertDialog.Material3.Animation = 0x7f0f0123
com.github.barteksc.pdfviewer.test:attr/buttonCompat = 0x7f030088
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_container_highest = 0x7f05027c
com.github.barteksc.pdfviewer.test:attr/itemTextAppearanceInactive = 0x7f03023b
com.github.barteksc.pdfviewer.test:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.github.barteksc.pdfviewer.test:attr/customIntegerValue = 0x7f03014d
com.github.barteksc.pdfviewer.test:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060230
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary20 = 0x7f050220
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f0f0106
com.github.barteksc.pdfviewer.test:attr/customFloatValue = 0x7f03014c
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0076
com.github.barteksc.pdfviewer.test:attr/tickColorActive = 0x7f03042d
com.github.barteksc.pdfviewer.test:styleable/MenuGroup = 0x7f10005b
com.github.barteksc.pdfviewer.test:attr/colorSurface = 0x7f030108
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f01b2
com.github.barteksc.pdfviewer.test:id/snackbar_action = 0x7f080167
com.github.barteksc.pdfviewer.test:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060101
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Button = 0x7f0f0019
com.github.barteksc.pdfviewer.test:attr/drawPath = 0x7f03016c
com.github.barteksc.pdfviewer.test:attr/behavior_overlapTop = 0x7f03006a
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.CompactMenu = 0x7f0f020c
com.github.barteksc.pdfviewer.test:attr/fabCradleVerticalOffset = 0x7f0301b5
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_year_corner = 0x7f06028f
com.github.barteksc.pdfviewer.test:drawable/m3_popupmenu_background_overlay = 0x7f0700a1
com.github.barteksc.pdfviewer.test:attr/customColorDrawableValue = 0x7f030149
com.github.barteksc.pdfviewer.test:attr/cornerSizeBottomRight = 0x7f03013a
com.github.barteksc.pdfviewer.test:attr/colorTertiary = 0x7f030113
com.github.barteksc.pdfviewer.test:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b0
com.github.barteksc.pdfviewer.test:attr/layout = 0x7f030248
com.github.barteksc.pdfviewer.test:attr/curveFit = 0x7f030147
com.github.barteksc.pdfviewer.test:attr/marginTopSystemWindowInsets = 0x7f0302a7
com.github.barteksc.pdfviewer.test:attr/cursorErrorColor = 0x7f030146
com.github.barteksc.pdfviewer.test:id/open_search_view_status_bar_spacer = 0x7f080128
com.github.barteksc.pdfviewer.test:id/leftToRight = 0x7f0800cb
com.github.barteksc.pdfviewer.test:id/checked = 0x7f080062
com.github.barteksc.pdfviewer.test:color/m3_timepicker_time_input_stroke_color = 0x7f0501fa
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f0f02b9
com.github.barteksc.pdfviewer.test:attr/labelStyle = 0x7f030243
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f0f016b
com.github.barteksc.pdfviewer.test:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601ca
com.github.barteksc.pdfviewer.test:attr/expandedTitleMarginStart = 0x7f0301a3
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant100 = 0x7f050212
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0015
com.github.barteksc.pdfviewer.test:attr/counterOverflowTextAppearance = 0x7f03013f
com.github.barteksc.pdfviewer.test:attr/backgroundTint = 0x7f03004c
com.github.barteksc.pdfviewer.test:color/material_on_background_emphasis_medium = 0x7f050251
com.github.barteksc.pdfviewer.test:attr/behavior_halfExpandedRatio = 0x7f030068
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060143
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501a3
com.github.barteksc.pdfviewer.test:drawable/abc_dialog_material_background = 0x7f07003b
com.github.barteksc.pdfviewer.test:attr/counterEnabled = 0x7f03013d
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002e
com.github.barteksc.pdfviewer.test:id/arc = 0x7f08004a
com.github.barteksc.pdfviewer.test:attr/telltales_velocityMode = 0x7f0303ea
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c0
com.github.barteksc.pdfviewer.test:attr/indeterminateProgressStyle = 0x7f030214
com.github.barteksc.pdfviewer.test:id/center_vertical = 0x7f08005e
com.github.barteksc.pdfviewer.test:drawable/navigation_empty_icon = 0x7f0700d7
com.github.barteksc.pdfviewer.test:attr/dayStyle = 0x7f030153
com.github.barteksc.pdfviewer.test:drawable/abc_cab_background_top_material = 0x7f070038
com.github.barteksc.pdfviewer.test:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f060234
com.github.barteksc.pdfviewer.test:color/m3_navigation_item_text_color = 0x7f050094
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Small = 0x7f0f002b
com.github.barteksc.pdfviewer.test:attr/initialActivityCount = 0x7f03021a
com.github.barteksc.pdfviewer.test:attr/errorShown = 0x7f030199
com.github.barteksc.pdfviewer.test:attr/cornerSize = 0x7f030138
com.github.barteksc.pdfviewer.test:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f0f02a6
com.github.barteksc.pdfviewer.test:animator/fragment_close_enter = 0x7f020003
com.github.barteksc.pdfviewer.test:anim/m3_motion_fade_enter = 0x7f010023
com.github.barteksc.pdfviewer.test:attr/behavior_hideable = 0x7f030069
com.github.barteksc.pdfviewer.test:attr/cornerFamilyTopLeft = 0x7f030135
com.github.barteksc.pdfviewer.test:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.github.barteksc.pdfviewer.test:layout/design_layout_tab_text = 0x7f0b0022
com.github.barteksc.pdfviewer.test:attr/contentInsetEnd = 0x7f030120
com.github.barteksc.pdfviewer.test:color/m3_sys_color_tertiary_fixed = 0x7f0501e1
com.github.barteksc.pdfviewer.test:attr/actionModeTheme = 0x7f03001d
com.github.barteksc.pdfviewer.test:dimen/notification_media_narrow_margin = 0x7f06030b
com.github.barteksc.pdfviewer.test:attr/coplanarSiblingViewId = 0x7f030131
com.github.barteksc.pdfviewer.test:attr/coordinatorLayoutStyle = 0x7f030130
com.github.barteksc.pdfviewer.test:attr/layout_constraintVertical_chainStyle = 0x7f030275
com.github.barteksc.pdfviewer.test:attr/colorOnSecondaryFixed = 0x7f0300f0
com.github.barteksc.pdfviewer.test:attr/thumbIconSize = 0x7f030423
com.github.barteksc.pdfviewer.test:attr/contrast = 0x7f03012e
com.github.barteksc.pdfviewer.test:id/autoCompleteToStart = 0x7f080050
com.github.barteksc.pdfviewer.test:attr/actionDropDownStyle = 0x7f03000c
com.github.barteksc.pdfviewer.test:attr/contentInsetStartWithNavigation = 0x7f030125
com.github.barteksc.pdfviewer.test:style/TextAppearance.MaterialComponents.Body2 = 0x7f0f01f9
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700c4
com.github.barteksc.pdfviewer.test:attr/backgroundInsetTop = 0x7f030048
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f032b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500ad
com.github.barteksc.pdfviewer.test:attr/contentInsetStart = 0x7f030124
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500b2
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f060150
com.github.barteksc.pdfviewer.test:id/action_image = 0x7f08003a
com.github.barteksc.pdfviewer.test:style/Widget.Compat.NotificationActionText = 0x7f0f032f
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f0f02a7
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f0212
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_default_width = 0x7f0602c6
com.github.barteksc.pdfviewer.test:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301ac
com.github.barteksc.pdfviewer.test:attr/content = 0x7f03011e
com.github.barteksc.pdfviewer.test:attr/animateNavigationIcon = 0x7f030030
com.github.barteksc.pdfviewer.test:color/m3_dynamic_default_color_secondary_text = 0x7f050081
com.github.barteksc.pdfviewer.test:attr/autoSizeMaxTextSize = 0x7f03003c
com.github.barteksc.pdfviewer.test:id/listMode = 0x7f0800d0
com.github.barteksc.pdfviewer.test:drawable/abc_text_cursor_material = 0x7f07006d
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f0f03d6
com.github.barteksc.pdfviewer.test:color/tooltip_background_light = 0x7f0502e6
com.github.barteksc.pdfviewer.test:attr/constraints = 0x7f03011d
com.github.barteksc.pdfviewer.test:styleable/ViewStubCompat = 0x7f100091
com.github.barteksc.pdfviewer.test:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602b8
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Button.Small = 0x7f0f02f5
com.github.barteksc.pdfviewer.test:attr/barrierMargin = 0x7f030062
com.github.barteksc.pdfviewer.test:attr/constraintSetEnd = 0x7f03011a
com.github.barteksc.pdfviewer.test:attr/colorOnSurfaceVariant = 0x7f0300f4
com.github.barteksc.pdfviewer.test:id/linear = 0x7f0800cf
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301c2
com.github.barteksc.pdfviewer.test:dimen/m3_btn_inset = 0x7f0600d7
com.github.barteksc.pdfviewer.test:attr/layout_editor_absoluteX = 0x7f03027c
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.EditText = 0x7f0f02fd
com.github.barteksc.pdfviewer.test:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ac
com.github.barteksc.pdfviewer.test:color/m3_navigation_item_ripple_color = 0x7f050093
com.github.barteksc.pdfviewer.test:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_error = 0x7f05014c
com.github.barteksc.pdfviewer.test:attr/colorSurfaceContainerLowest = 0x7f03010e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060121
com.github.barteksc.pdfviewer.test:attr/backgroundInsetStart = 0x7f030047
com.github.barteksc.pdfviewer.test:attr/colorSurfaceContainer = 0x7f03010a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060172
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Placeholder = 0x7f0f01ce
com.github.barteksc.pdfviewer.test:attr/colorSecondary = 0x7f030103
com.github.barteksc.pdfviewer.test:attr/motionDurationShort3 = 0x7f0302ff
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_10 = 0x7f080012
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f0f03e3
com.github.barteksc.pdfviewer.test:attr/colorPrimaryInverse = 0x7f030100
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f0f0086
com.github.barteksc.pdfviewer.test:attr/perpendicularPath_percent = 0x7f030342
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f0f006c
com.github.barteksc.pdfviewer.test:id/transition_layout_save = 0x7f0801a9
com.github.barteksc.pdfviewer.test:animator/fragment_fade_exit = 0x7f020006
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_default_width = 0x7f0601c2
com.github.barteksc.pdfviewer.test:attr/cardBackgroundColor = 0x7f030093
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f0f01c8
com.github.barteksc.pdfviewer.test:id/triangle = 0x7f0801ad
com.github.barteksc.pdfviewer.test:attr/showDividers = 0x7f030386
com.github.barteksc.pdfviewer.test:attr/arrowShaftLength = 0x7f030037
com.github.barteksc.pdfviewer.test:attr/colorPrimaryDark = 0x7f0300fd
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f0f0283
com.github.barteksc.pdfviewer.test:color/design_default_color_secondary_variant = 0x7f050049
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f0f042a
com.github.barteksc.pdfviewer.test:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0601f7
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f0146
com.github.barteksc.pdfviewer.test:attr/colorPrimaryContainer = 0x7f0300fc
com.github.barteksc.pdfviewer.test:attr/checkedTextViewStyle = 0x7f0300ab
com.github.barteksc.pdfviewer.test:attr/marginLeftSystemWindowInsets = 0x7f0302a5
com.github.barteksc.pdfviewer.test:color/switch_thumb_normal_material_light = 0x7f0502e4
com.github.barteksc.pdfviewer.test:attr/colorOnTertiaryContainer = 0x7f0300f6
com.github.barteksc.pdfviewer.test:attr/clockHandColor = 0x7f0300c4
com.github.barteksc.pdfviewer.test:string/abc_searchview_description_search = 0x7f0e0015
com.github.barteksc.pdfviewer.test:id/material_label = 0x7f0800e0
com.github.barteksc.pdfviewer.test:attr/colorSecondaryContainer = 0x7f030104
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary70 = 0x7f05023f
com.github.barteksc.pdfviewer.test:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.github.barteksc.pdfviewer.test:attr/drawerLayoutCornerSize = 0x7f030177
com.github.barteksc.pdfviewer.test:layout/design_navigation_item_header = 0x7f0b0025
com.github.barteksc.pdfviewer.test:attr/motionEasingEmphasizedInterpolator = 0x7f030306
com.github.barteksc.pdfviewer.test:attr/buttonIconTintMode = 0x7f03008d
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_primary_container = 0x7f050155
com.github.barteksc.pdfviewer.test:attr/chipMinTouchTargetSize = 0x7f0300b6
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f0f02b6
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_primary_container = 0x7f050265
com.github.barteksc.pdfviewer.test:attr/colorOnSurface = 0x7f0300f2
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Empty = 0x7f0f0218
com.github.barteksc.pdfviewer.test:style/Base.Theme.MaterialComponents.Dialog = 0x7f0f0068
com.github.barteksc.pdfviewer.test:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601d1
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f01bc
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f01b5
com.github.barteksc.pdfviewer.test:attr/layout_behavior = 0x7f03024e
com.github.barteksc.pdfviewer.test:attr/colorOnPrimary = 0x7f0300e9
com.github.barteksc.pdfviewer.test:attr/largeFontVerticalOffsetAdjustment = 0x7f030245
com.github.barteksc.pdfviewer.test:attr/actionProviderClass = 0x7f030021
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0012
com.github.barteksc.pdfviewer.test:attr/showAnimationBehavior = 0x7f030383
com.github.barteksc.pdfviewer.test:color/material_personalized_color_on_surface = 0x7f050268
com.github.barteksc.pdfviewer.test:color/m3_timepicker_button_ripple_color = 0x7f0501f2
com.github.barteksc.pdfviewer.test:attr/checkMarkTint = 0x7f03009e
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f0f044b
com.github.barteksc.pdfviewer.test:attr/colorOnError = 0x7f0300e7
com.github.barteksc.pdfviewer.test:attr/closeIconVisible = 0x7f0300cd
com.github.barteksc.pdfviewer.test:attr/colorOnContainer = 0x7f0300e5
com.github.barteksc.pdfviewer.test:integer/m3_sys_motion_duration_short4 = 0x7f09001e
com.github.barteksc.pdfviewer.test:attr/colorSurfaceVariant = 0x7f030111
com.github.barteksc.pdfviewer.test:attr/thumbColor = 0x7f030420
com.github.barteksc.pdfviewer.test:id/snapMargins = 0x7f08016a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f05018a
com.github.barteksc.pdfviewer.test:attr/colorControlHighlight = 0x7f0300e0
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_sheet_elevation = 0x7f0600c3
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_background_corner_radius = 0x7f060080
com.github.barteksc.pdfviewer.test:attr/buttonIconTint = 0x7f03008c
com.github.barteksc.pdfviewer.test:attr/drawableTint = 0x7f030173
com.github.barteksc.pdfviewer.test:attr/motion_triggerOnCollision = 0x7f030314
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_header_dialog = 0x7f0b0056
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_on_background = 0x7f050033
com.github.barteksc.pdfviewer.test:attr/fabCradleRoundedCornerRadius = 0x7f0301b4
com.github.barteksc.pdfviewer.test:color/abc_tint_spinner = 0x7f050017
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f0f037c
com.github.barteksc.pdfviewer.test:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300d6
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.UnelevatedButton = 0x7f0f035e
com.github.barteksc.pdfviewer.test:attr/colorSurfaceDim = 0x7f03010f
com.github.barteksc.pdfviewer.test:layout/material_clockface_textview = 0x7f0b0036
com.github.barteksc.pdfviewer.test:drawable/m3_avd_hide_password = 0x7f07009d
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f050187
com.github.barteksc.pdfviewer.test:attr/barLength = 0x7f03005f
com.github.barteksc.pdfviewer.test:attr/selectorSize = 0x7f030375
com.github.barteksc.pdfviewer.test:attr/placeholderTextColor = 0x7f030346
com.github.barteksc.pdfviewer.test:styleable/RecycleListView = 0x7f10006f
com.github.barteksc.pdfviewer.test:attr/colorTertiaryFixed = 0x7f030115
com.github.barteksc.pdfviewer.test:attr/layout_constraintGuide_begin = 0x7f03025e
com.github.barteksc.pdfviewer.test:attr/collapseIcon = 0x7f0300d0
com.github.barteksc.pdfviewer.test:dimen/design_fab_border_width = 0x7f06006e
com.github.barteksc.pdfviewer.test:drawable/mtrl_dropdown_arrow = 0x7f0700be
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f0f040a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral6 = 0x7f050102
com.github.barteksc.pdfviewer.test:id/split_action_bar = 0x7f08016e
com.github.barteksc.pdfviewer.test:attr/clockNumberTextColor = 0x7f0300c6
com.github.barteksc.pdfviewer.test:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.github.barteksc.pdfviewer.test:attr/fabAlignmentMode = 0x7f0301af
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.github.barteksc.pdfviewer.test:attr/clockIcon = 0x7f0300c5
com.github.barteksc.pdfviewer.test:attr/liftOnScrollTargetViewId = 0x7f03028c
com.github.barteksc.pdfviewer.test:style/TextAppearance.Design.Counter.Overflow = 0x7f0f01ca
com.github.barteksc.pdfviewer.test:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700b8
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500a6
com.github.barteksc.pdfviewer.test:attr/customColorValue = 0x7f03014a
com.github.barteksc.pdfviewer.test:attr/color = 0x7f0300da
com.github.barteksc.pdfviewer.test:attr/fontProviderFetchStrategy = 0x7f0301e4
com.github.barteksc.pdfviewer.test:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.github.barteksc.pdfviewer.test:attr/iconGravity = 0x7f03020b
com.github.barteksc.pdfviewer.test:attr/shapeCornerFamily = 0x7f030380
com.github.barteksc.pdfviewer.test:attr/materialCircleRadius = 0x7f0302c4
com.github.barteksc.pdfviewer.test:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f060187
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadline3 = 0x7f0303f8
com.github.barteksc.pdfviewer.test:attr/boxCornerRadiusBottomEnd = 0x7f03007a
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f0f0443
com.github.barteksc.pdfviewer.test:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0172
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_navigation_height = 0x7f060285
com.github.barteksc.pdfviewer.test:attr/clickAction = 0x7f0300c2
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary60 = 0x7f050231
com.github.barteksc.pdfviewer.test:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070043
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c008b
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060144
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_disabled_elevation = 0x7f060254
com.github.barteksc.pdfviewer.test:attr/paddingTopNoTitle = 0x7f030332
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f0f02e0
com.github.barteksc.pdfviewer.test:attr/onTouchUp = 0x7f030328
com.github.barteksc.pdfviewer.test:anim/design_snackbar_in = 0x7f01001a
com.github.barteksc.pdfviewer.test:attr/drawableLeftCompat = 0x7f03016f
com.github.barteksc.pdfviewer.test:id/useLogo = 0x7f0801b2
com.github.barteksc.pdfviewer.test:attr/cardMaxElevation = 0x7f030097
com.github.barteksc.pdfviewer.test:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f06020e
com.github.barteksc.pdfviewer.test:id/navigation_bar_item_active_indicator_view = 0x7f08010d
com.github.barteksc.pdfviewer.test:attr/circularProgressIndicatorStyle = 0x7f0300c1
com.github.barteksc.pdfviewer.test:attr/subtitleTextStyle = 0x7f0303c1
com.github.barteksc.pdfviewer.test:style/Base.Animation.AppCompat.Dialog = 0x7f0f000d
com.github.barteksc.pdfviewer.test:attr/actionMenuTextColor = 0x7f03000f
com.github.barteksc.pdfviewer.test:attr/progressBarStyle = 0x7f030352
com.github.barteksc.pdfviewer.test:styleable/MaterialAlertDialog = 0x7f100049
com.github.barteksc.pdfviewer.test:color/material_dynamic_primary40 = 0x7f050222
com.github.barteksc.pdfviewer.test:animator/m3_card_state_list_anim = 0x7f02000d
com.github.barteksc.pdfviewer.test:attr/chipStandaloneStyle = 0x7f0300ba
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001c
com.github.barteksc.pdfviewer.test:attr/trackColorInactive = 0x7f030457
com.github.barteksc.pdfviewer.test:attr/chipIconVisible = 0x7f0300b4
com.github.barteksc.pdfviewer.test:attr/dividerInsetEnd = 0x7f030164
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f06028b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500d6
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error60 = 0x7f0500f0
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadline1 = 0x7f0303f6
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_dark_surface = 0x7f050180
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0014
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral10 = 0x7f05009e
com.github.barteksc.pdfviewer.test:styleable/ViewBackgroundHelper = 0x7f10008f
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602bd
com.github.barteksc.pdfviewer.test:id/text_input_error_icon = 0x7f080196
com.github.barteksc.pdfviewer.test:attr/cardElevation = 0x7f030095
com.github.barteksc.pdfviewer.test:attr/lastItemDecorated = 0x7f030247
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000d
com.github.barteksc.pdfviewer.test:attr/colorSecondaryVariant = 0x7f030107
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500c0
com.github.barteksc.pdfviewer.test:attr/checkedIcon = 0x7f0300a3
com.github.barteksc.pdfviewer.test:attr/checkedIconMargin = 0x7f0300a6
com.github.barteksc.pdfviewer.test:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f0f018b
com.github.barteksc.pdfviewer.test:attr/checkedButton = 0x7f0300a1
com.github.barteksc.pdfviewer.test:attr/editTextBackground = 0x7f03017e
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f0f0155
com.github.barteksc.pdfviewer.test:attr/actionMenuTextAppearance = 0x7f03000e
com.github.barteksc.pdfviewer.test:id/ignoreRequest = 0x7f0800be
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601b7
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500c1
com.github.barteksc.pdfviewer.test:attr/indicatorSize = 0x7f030219
com.github.barteksc.pdfviewer.test:dimen/m3_comp_search_bar_container_height = 0x7f06016e
com.github.barteksc.pdfviewer.test:attr/badgeTextColor = 0x7f030056
com.github.barteksc.pdfviewer.test:styleable/ButtonBarLayout = 0x7f100018
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602ad
com.github.barteksc.pdfviewer.test:attr/buttonStyle = 0x7f03008f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_outline_variant = 0x7f05015d
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f0148
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_elevation = 0x7f0602be
com.github.barteksc.pdfviewer.test:attr/alertDialogCenterButtons = 0x7f030028
com.github.barteksc.pdfviewer.test:attr/buttonIcon = 0x7f03008a
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f0276
com.github.barteksc.pdfviewer.test:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f0f0060
com.github.barteksc.pdfviewer.test:layout/design_navigation_menu = 0x7f0b0028
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_size_compact = 0x7f0600a9
com.github.barteksc.pdfviewer.test:color/material_on_surface_disabled = 0x7f050255
com.github.barteksc.pdfviewer.test:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601d8
com.github.barteksc.pdfviewer.test:color/material_personalized_color_control_highlight = 0x7f05025d
com.github.barteksc.pdfviewer.test:id/notification_main_column_container = 0x7f08011a
com.github.barteksc.pdfviewer.test:attr/closeIconSize = 0x7f0300ca
com.github.barteksc.pdfviewer.test:id/position = 0x7f080139
com.github.barteksc.pdfviewer.test:attr/chipStyle = 0x7f0300be
com.github.barteksc.pdfviewer.test:attr/waveShape = 0x7f030476
com.github.barteksc.pdfviewer.test:attr/buttonStyleSmall = 0x7f030090
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f0f0359
com.github.barteksc.pdfviewer.test:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.github.barteksc.pdfviewer.test:integer/mtrl_calendar_header_orientation = 0x7f090030
com.github.barteksc.pdfviewer.test:color/switch_thumb_material_light = 0x7f0502e2
com.github.barteksc.pdfviewer.test:attr/itemIconSize = 0x7f030226
com.github.barteksc.pdfviewer.test:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fd
com.github.barteksc.pdfviewer.test:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0016
com.github.barteksc.pdfviewer.test:attr/materialAlertDialogTitlePanelStyle = 0x7f0302ac
com.github.barteksc.pdfviewer.test:attr/actionModeCloseButtonStyle = 0x7f030011
com.github.barteksc.pdfviewer.test:drawable/default_scroll_handle_top = 0x7f070084
com.github.barteksc.pdfviewer.test:dimen/design_textinput_caption_translate_y = 0x7f06008d
com.github.barteksc.pdfviewer.test:id/unlabeled = 0x7f0801b0
com.github.barteksc.pdfviewer.test:attr/subtitle = 0x7f0303bd
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary70 = 0x7f050123
com.github.barteksc.pdfviewer.test:id/fade = 0x7f08009e
com.github.barteksc.pdfviewer.test:attr/layoutDuringTransition = 0x7f03024a
com.github.barteksc.pdfviewer.test:attr/yearStyle = 0x7f030483
com.github.barteksc.pdfviewer.test:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f0f037f
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0054
com.github.barteksc.pdfviewer.test:attr/backgroundColor = 0x7f030044
com.github.barteksc.pdfviewer.test:attr/colorSurfaceContainerHigh = 0x7f03010b
com.github.barteksc.pdfviewer.test:layout/material_timepicker_textinput_display = 0x7f0b003e
com.github.barteksc.pdfviewer.test:attr/colorErrorContainer = 0x7f0300e3
com.github.barteksc.pdfviewer.test:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0028
com.github.barteksc.pdfviewer.test:attr/iconifiedByDefault = 0x7f030211
com.github.barteksc.pdfviewer.test:attr/itemIconTint = 0x7f030227
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_30 = 0x7f080028
com.github.barteksc.pdfviewer.test:attr/appBarLayoutStyle = 0x7f030033
com.github.barteksc.pdfviewer.test:attr/layout_goneMarginEnd = 0x7f03027f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_secondary_fixed = 0x7f0501df
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_background = 0x7f050151
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f0f0270
com.github.barteksc.pdfviewer.test:attr/boxStrokeColor = 0x7f03007e
com.github.barteksc.pdfviewer.test:id/navigation_bar_item_small_label_view = 0x7f080112
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadlineSmall = 0x7f0303fe
com.github.barteksc.pdfviewer.test:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
com.github.barteksc.pdfviewer.test:dimen/m3_sys_elevation_level4 = 0x7f0601ed
com.github.barteksc.pdfviewer.test:animator/m3_appbar_state_list_animator = 0x7f020009
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f0024
com.github.barteksc.pdfviewer.test:string/mtrl_picker_announce_current_selection = 0x7f0e006e
com.github.barteksc.pdfviewer.test:color/material_on_surface_emphasis_high_type = 0x7f050256
com.github.barteksc.pdfviewer.test:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c010b
com.github.barteksc.pdfviewer.test:attr/checkedIconVisible = 0x7f0300a9
com.github.barteksc.pdfviewer.test:attr/contentPaddingEnd = 0x7f030128
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00fb
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060138
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_primary90 = 0x7f050125
com.github.barteksc.pdfviewer.test:anim/abc_slide_in_bottom = 0x7f010006
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_0 = 0x7f080010
com.github.barteksc.pdfviewer.test:attr/materialAlertDialogTheme = 0x7f0302aa
com.github.barteksc.pdfviewer.test:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070026
com.github.barteksc.pdfviewer.test:macro/m3_comp_elevated_card_container_shape = 0x7f0c002c
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadline5 = 0x7f0303fa
com.github.barteksc.pdfviewer.test:attr/headerLayout = 0x7f0301f5
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f0f0112
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_23 = 0x7f080020
com.github.barteksc.pdfviewer.test:attr/fastScrollVerticalTrackDrawable = 0x7f0301bc
com.github.barteksc.pdfviewer.test:attr/alertDialogButtonGroupStyle = 0x7f030027
com.github.barteksc.pdfviewer.test:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f0f0232
com.github.barteksc.pdfviewer.test:attr/closeIconEnabled = 0x7f0300c8
com.github.barteksc.pdfviewer.test:attr/animateMenuItems = 0x7f03002f
com.github.barteksc.pdfviewer.test:color/background_floating_material_dark = 0x7f05001d
com.github.barteksc.pdfviewer.test:layout/mtrl_calendar_vertical = 0x7f0b004e
com.github.barteksc.pdfviewer.test:attr/titleMarginStart = 0x7f03043f
com.github.barteksc.pdfviewer.test:attr/bottomSheetDialogTheme = 0x7f030074
com.github.barteksc.pdfviewer.test:color/material_personalized_color_secondary_text = 0x7f050276
com.github.barteksc.pdfviewer.test:attr/counterOverflowTextColor = 0x7f030140
com.github.barteksc.pdfviewer.test:string/material_clock_display_divider = 0x7f0e0046
com.github.barteksc.pdfviewer.test:id/animateToStart = 0x7f080049
com.github.barteksc.pdfviewer.test:attr/closeItemLayout = 0x7f0300ce
com.github.barteksc.pdfviewer.test:attr/behavior_skipCollapsed = 0x7f03006e
com.github.barteksc.pdfviewer.test:attr/cornerFamilyBottomRight = 0x7f030134
com.github.barteksc.pdfviewer.test:id/open_search_view_background = 0x7f08011e
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_thumb_radius = 0x7f0602e3
com.github.barteksc.pdfviewer.test:attr/controlBackground = 0x7f03012f
com.github.barteksc.pdfviewer.test:string/bottomsheet_action_expand_halfway = 0x7f0e0020
com.github.barteksc.pdfviewer.test:attr/queryPatterns = 0x7f030355
com.github.barteksc.pdfviewer.test:dimen/compat_notification_large_icon_max_width = 0x7f06005c
com.github.barteksc.pdfviewer.test:attr/keylines = 0x7f030240
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_tertiary50 = 0x7f05013b
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500d9
com.github.barteksc.pdfviewer.test:attr/altSrc = 0x7f03002e
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light.Dialog = 0x7f0f021b
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f01bf
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f00d0
com.github.barteksc.pdfviewer.test:attr/colorSurfaceContainerLow = 0x7f03010d
com.github.barteksc.pdfviewer.test:id/material_clock_display_and_toggle = 0x7f0800d7
com.github.barteksc.pdfviewer.test:attr/number = 0x7f030320
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f0f036c
com.github.barteksc.pdfviewer.test:attr/materialCardViewElevatedStyle = 0x7f0302c0
com.github.barteksc.pdfviewer.test:attr/verticalOffsetWithText = 0x7f03046e
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_title_baseline_to_top = 0x7f06028d
com.github.barteksc.pdfviewer.test:attr/actionModeSelectAllDrawable = 0x7f030019
com.github.barteksc.pdfviewer.test:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
com.github.barteksc.pdfviewer.test:attr/materialTimePickerStyle = 0x7f0302d4
com.github.barteksc.pdfviewer.test:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301ad
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060082
com.github.barteksc.pdfviewer.test:style/Base.Widget.AppCompat.SearchView = 0x7f0f00f2
com.github.barteksc.pdfviewer.test:attr/chipIcon = 0x7f0300b0
com.github.barteksc.pdfviewer.test:id/tag_accessibility_pane_title = 0x7f080184
com.github.barteksc.pdfviewer.test:attr/colorAccent = 0x7f0300db
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_elevation = 0x7f0601c3
com.github.barteksc.pdfviewer.test:string/mtrl_picker_save = 0x7f0e0083
com.github.barteksc.pdfviewer.test:attr/badgeText = 0x7f030054
com.github.barteksc.pdfviewer.test:attr/nestedScrollFlags = 0x7f03031d
com.github.barteksc.pdfviewer.test:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500e7
com.github.barteksc.pdfviewer.test:id/floating = 0x7f0800aa
com.github.barteksc.pdfviewer.test:drawable/abc_seekbar_track_material = 0x7f070064
com.github.barteksc.pdfviewer.test:attr/dragScale = 0x7f03016a
com.github.barteksc.pdfviewer.test:style/Widget.Material3.Toolbar = 0x7f0f03da
com.github.barteksc.pdfviewer.test:color/m3_sys_color_tertiary_fixed_dim = 0x7f0501e2
com.github.barteksc.pdfviewer.test:attr/behavior_draggable = 0x7f030065
com.github.barteksc.pdfviewer.test:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.github.barteksc.pdfviewer.test:dimen/m3_simple_item_color_selected_alpha = 0x7f0601e2
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060118
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_dialog_background_inset = 0x7f060276
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f02f8
com.github.barteksc.pdfviewer.test:attr/hideAnimationBehavior = 0x7f0301fb
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_on_primary = 0x7f050190
com.github.barteksc.pdfviewer.test:attr/boxStrokeWidthFocused = 0x7f030081
com.github.barteksc.pdfviewer.test:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070046
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_icon_size = 0x7f0600a2
com.github.barteksc.pdfviewer.test:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0501da
com.github.barteksc.pdfviewer.test:attr/barrierAllowsGoneWidgets = 0x7f030060
com.github.barteksc.pdfviewer.test:attr/motionEasingEmphasized = 0x7f030303
com.github.barteksc.pdfviewer.test:drawable/notification_bg_normal = 0x7f0700dd
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f0f0161
com.github.barteksc.pdfviewer.test:attr/cardCornerRadius = 0x7f030094
com.github.barteksc.pdfviewer.test:attr/actionModeStyle = 0x7f03001c
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f030c
com.github.barteksc.pdfviewer.test:animator/m3_chip_state_list_anim = 0x7f02000e
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013d
com.github.barteksc.pdfviewer.test:attr/thumbIconTint = 0x7f030424
com.github.barteksc.pdfviewer.test:attr/colorTertiaryFixedDim = 0x7f030116
com.github.barteksc.pdfviewer.test:attr/multiChoiceItemLayout = 0x7f030316
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00d0
com.github.barteksc.pdfviewer.test:dimen/design_fab_elevation = 0x7f06006f
com.github.barteksc.pdfviewer.test:styleable/OnSwipe = 0x7f100069
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b6
com.github.barteksc.pdfviewer.test:attr/hideOnScroll = 0x7f0301ff
com.github.barteksc.pdfviewer.test:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015e
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500be
com.github.barteksc.pdfviewer.test:macro/m3_comp_filled_button_label_text_color = 0x7f0c0045
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06011f
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_surface_variant = 0x7f050159
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.Display3 = 0x7f0f019a
com.github.barteksc.pdfviewer.test:attr/itemShapeFillColor = 0x7f030230
com.github.barteksc.pdfviewer.test:attr/helperTextTextAppearance = 0x7f0301f9
com.github.barteksc.pdfviewer.test:attr/itemTextColor = 0x7f03023c
com.github.barteksc.pdfviewer.test:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a5
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_secondary30 = 0x7f05012c
com.github.barteksc.pdfviewer.test:attr/firstBaselineToTopHeight = 0x7f0301bd
com.github.barteksc.pdfviewer.test:attr/dividerThickness = 0x7f030167
com.github.barteksc.pdfviewer.test:attr/itemBackground = 0x7f030221
com.github.barteksc.pdfviewer.test:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011d
com.github.barteksc.pdfviewer.test:attr/checkMarkCompat = 0x7f03009d
com.github.barteksc.pdfviewer.test:attr/actionModeCloseDrawable = 0x7f030013
com.github.barteksc.pdfviewer.test:attr/badgeTextAppearance = 0x7f030055
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_month_horizontal_padding = 0x7f060282
com.github.barteksc.pdfviewer.test:styleable/CompoundButton = 0x7f100024
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0083
com.github.barteksc.pdfviewer.test:attr/motion_postLayoutCollision = 0x7f030313
com.github.barteksc.pdfviewer.test:attr/actionLayout = 0x7f03000d
com.github.barteksc.pdfviewer.test:attr/closeIconStartPadding = 0x7f0300cb
com.github.barteksc.pdfviewer.test:attr/actionModeCopyDrawable = 0x7f030014
com.github.barteksc.pdfviewer.test:attr/cornerRadius = 0x7f030137
com.github.barteksc.pdfviewer.test:attr/deltaPolarRadius = 0x7f03015b
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.TitleMedium = 0x7f0f01f5
com.github.barteksc.pdfviewer.test:attr/customStringValue = 0x7f030150
com.github.barteksc.pdfviewer.test:layout/mtrl_alert_dialog_actions = 0x7f0b0040
com.github.barteksc.pdfviewer.test:animator/m3_btn_state_list_anim = 0x7f02000b
com.github.barteksc.pdfviewer.test:anim/design_bottom_sheet_slide_in = 0x7f010018
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant10 = 0x7f050211
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f021e
com.github.barteksc.pdfviewer.test:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f0f00aa
com.github.barteksc.pdfviewer.test:attr/dialogTheme = 0x7f03015f
com.github.barteksc.pdfviewer.test:attr/collapseContentDescription = 0x7f0300cf
com.github.barteksc.pdfviewer.test:attr/badgeHeight = 0x7f03004f
com.github.barteksc.pdfviewer.test:attr/percentHeight = 0x7f03033e
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f0f025a
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f003e
com.github.barteksc.pdfviewer.test:attr/expandedTitleMarginTop = 0x7f0301a4
com.github.barteksc.pdfviewer.test:styleable/Variant = 0x7f10008d
com.github.barteksc.pdfviewer.test:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f0f0159
com.github.barteksc.pdfviewer.test:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00cc
com.github.barteksc.pdfviewer.test:attr/badgeWithTextRadius = 0x7f03005b
com.github.barteksc.pdfviewer.test:attr/colorOnPrimaryContainer = 0x7f0300ea
com.github.barteksc.pdfviewer.test:id/fragment_container_view_tag = 0x7f0800ac
com.github.barteksc.pdfviewer.test:color/abc_tint_seek_thumb = 0x7f050016
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.BodyLarge = 0x7f0f01e4
com.github.barteksc.pdfviewer.test:attr/actionBarTabStyle = 0x7f030007
com.github.barteksc.pdfviewer.test:attr/backgroundInsetEnd = 0x7f030046
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary80 = 0x7f050240
com.github.barteksc.pdfviewer.test:string/m3_ref_typeface_brand_regular = 0x7f0e0038
com.github.barteksc.pdfviewer.test:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f050095
com.github.barteksc.pdfviewer.test:attr/elevationOverlayEnabled = 0x7f030184
com.github.barteksc.pdfviewer.test:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f0f0110
com.github.barteksc.pdfviewer.test:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.github.barteksc.pdfviewer.test:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060103
com.github.barteksc.pdfviewer.test:id/item_touch_helper_previous_elevation = 0x7f0800c5
com.github.barteksc.pdfviewer.test:id/ifRoom = 0x7f0800bc
com.github.barteksc.pdfviewer.test:attr/errorTextAppearance = 0x7f03019a
com.github.barteksc.pdfviewer.test:id/marquee = 0x7f0800d3
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonStyle = 0x7f0301ca
com.github.barteksc.pdfviewer.test:attr/autoTransition = 0x7f030041
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601b8
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f0f0374
com.github.barteksc.pdfviewer.test:attr/autoSizePresetSizes = 0x7f03003e
com.github.barteksc.pdfviewer.test:color/material_personalized__highlighted_text = 0x7f050259
com.github.barteksc.pdfviewer.test:attr/boxCollapsedPaddingTop = 0x7f030079
com.github.barteksc.pdfviewer.test:color/mtrl_scrim_color = 0x7f0502bf
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral99 = 0x7f05010d
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500dc
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a4
com.github.barteksc.pdfviewer.test:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f060193
com.github.barteksc.pdfviewer.test:anim/m3_motion_fade_exit = 0x7f010024
com.github.barteksc.pdfviewer.test:attr/layout_dodgeInsetEdges = 0x7f03027b
com.github.barteksc.pdfviewer.test:attr/arrowHeadLength = 0x7f030036
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bd
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a5
com.github.barteksc.pdfviewer.test:attr/alpha = 0x7f03002c
com.github.barteksc.pdfviewer.test:attr/windowFixedWidthMinor = 0x7f03047e
com.github.barteksc.pdfviewer.test:color/material_dynamic_secondary100 = 0x7f05022c
com.github.barteksc.pdfviewer.test:attr/textAppearanceLabelSmall = 0x7f030401
com.github.barteksc.pdfviewer.test:attr/textAppearanceHeadline4 = 0x7f0303f9
com.github.barteksc.pdfviewer.test:attr/chainUseRtl = 0x7f03009c
com.github.barteksc.pdfviewer.test:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602ac
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral60 = 0x7f050103
com.github.barteksc.pdfviewer.test:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602b6
com.github.barteksc.pdfviewer.test:dimen/mtrl_high_ripple_default_alpha = 0x7f0602b3
com.github.barteksc.pdfviewer.test:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.github.barteksc.pdfviewer.test:style/Widget.Material3.CheckedTextView = 0x7f0f0362
com.github.barteksc.pdfviewer.test:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0034
com.github.barteksc.pdfviewer.test:attr/layout_constraintBaseline_toBaselineOf = 0x7f030254
com.github.barteksc.pdfviewer.test:id/touch_outside = 0x7f0801a5
com.github.barteksc.pdfviewer.test:attr/alertDialogTheme = 0x7f03002a
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.Light = 0x7f0f0052
com.github.barteksc.pdfviewer.test:attr/materialButtonStyle = 0x7f0302af
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_input_text_type = 0x7f0c00eb
com.github.barteksc.pdfviewer.test:drawable/notification_bg_normal_pressed = 0x7f0700de
com.github.barteksc.pdfviewer.test:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090020
com.github.barteksc.pdfviewer.test:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060289
com.github.barteksc.pdfviewer.test:color/design_default_color_on_secondary = 0x7f050043
com.github.barteksc.pdfviewer.test:attr/alertDialogStyle = 0x7f030029
com.github.barteksc.pdfviewer.test:attr/itemHorizontalPadding = 0x7f030223
com.github.barteksc.pdfviewer.test:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f0f0080
com.github.barteksc.pdfviewer.test:attr/titleTextAppearance = 0x7f030443
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_autocomplete_menu_list_item_selected_container_color = 0x7f0c00a2
com.github.barteksc.pdfviewer.test:attr/behavior_expandedOffset = 0x7f030066
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface_container_low = 0x7f05027d
com.github.barteksc.pdfviewer.test:attr/autoShowKeyboard = 0x7f03003b
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f014e
com.github.barteksc.pdfviewer.test:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.github.barteksc.pdfviewer.test:style/Platform.MaterialComponents.Light = 0x7f0f013a
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary95 = 0x7f0500cd
com.github.barteksc.pdfviewer.test:attr/titleMarginEnd = 0x7f03043e
com.github.barteksc.pdfviewer.test:attr/listMenuViewStyle = 0x7f030297
com.github.barteksc.pdfviewer.test:color/material_dynamic_neutral_variant30 = 0x7f050214
com.github.barteksc.pdfviewer.test:attr/collapsedTitleGravity = 0x7f0300d2
com.github.barteksc.pdfviewer.test:style/Theme.Design.Light = 0x7f0f0223
com.github.barteksc.pdfviewer.test:attr/titleMargins = 0x7f030441
com.github.barteksc.pdfviewer.test:attr/mock_diagonalsColor = 0x7f0302ea
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f0f02c0
com.github.barteksc.pdfviewer.test:attr/colorSecondaryFixedDim = 0x7f030106
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0082
com.github.barteksc.pdfviewer.test:dimen/appcompat_dialog_background_inset = 0x7f060051
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TabLayout = 0x7f0f03cb
com.github.barteksc.pdfviewer.test:attr/colorControlActivated = 0x7f0300df
com.github.barteksc.pdfviewer.test:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.github.barteksc.pdfviewer.test:animator/design_fab_hide_motion_spec = 0x7f020001
com.github.barteksc.pdfviewer.test:macro/m3_comp_fab_surface_container_color = 0x7f0c003e
com.github.barteksc.pdfviewer.test:dimen/mtrl_snackbar_margin = 0x7f0602eb
com.github.barteksc.pdfviewer.test:color/material_timepicker_button_stroke = 0x7f050294
com.github.barteksc.pdfviewer.test:attr/liftOnScroll = 0x7f03028a
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_secondary = 0x7f0501ca
com.github.barteksc.pdfviewer.test:style/Widget.MaterialComponents.FloatingActionButton = 0x7f0f0410
com.github.barteksc.pdfviewer.test:id/mtrl_picker_header = 0x7f080103
com.github.barteksc.pdfviewer.test:attr/placeholderText = 0x7f030344
com.github.barteksc.pdfviewer.test:attr/height = 0x7f0301f6
com.github.barteksc.pdfviewer.test:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f0f00a6
com.github.barteksc.pdfviewer.test:layout/material_time_input = 0x7f0b003b
com.github.barteksc.pdfviewer.test:dimen/m3_card_elevated_disabled_z = 0x7f0600e6
com.github.barteksc.pdfviewer.test:attr/chipCornerRadius = 0x7f0300ad
com.github.barteksc.pdfviewer.test:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f0f03d3
com.github.barteksc.pdfviewer.test:drawable/abc_list_pressed_holo_light = 0x7f070051
com.github.barteksc.pdfviewer.test:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.github.barteksc.pdfviewer.test:attr/materialIconButtonFilledStyle = 0x7f0302c9
com.github.barteksc.pdfviewer.test:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f0f0111
com.github.barteksc.pdfviewer.test:color/mtrl_popupmenu_overlay_color = 0x7f0502be
com.github.barteksc.pdfviewer.test:attr/itemStrokeColor = 0x7f030236
com.github.barteksc.pdfviewer.test:dimen/m3_comp_suggestion_chip_container_height = 0x7f060184
com.github.barteksc.pdfviewer.test:attr/layout_constraintWidth_default = 0x7f030277
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601c5
com.github.barteksc.pdfviewer.test:attr/actionTextColorAlpha = 0x7f030022
com.github.barteksc.pdfviewer.test:attr/paddingEnd = 0x7f03032d
com.github.barteksc.pdfviewer.test:dimen/m3_bottomappbar_height = 0x7f0600c9
com.github.barteksc.pdfviewer.test:dimen/m3_alert_dialog_corner_size = 0x7f06009f
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500e3
com.github.barteksc.pdfviewer.test:attr/itemShapeAppearanceOverlay = 0x7f03022f
com.github.barteksc.pdfviewer.test:drawable/ic_call_decline = 0x7f07008f
com.github.barteksc.pdfviewer.test:attr/fastScrollVerticalThumbDrawable = 0x7f0301bb
com.github.barteksc.pdfviewer.test:attr/actionModeFindDrawable = 0x7f030016
com.github.barteksc.pdfviewer.test:attr/badgeWidth = 0x7f030059
com.github.barteksc.pdfviewer.test:attr/flow_horizontalGap = 0x7f0301d3
com.github.barteksc.pdfviewer.test:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.SearchView.Prefix = 0x7f0f01f3
com.github.barteksc.pdfviewer.test:attr/closeIconTint = 0x7f0300cc
com.github.barteksc.pdfviewer.test:attr/constraint_referenced_ids = 0x7f03011c
com.github.barteksc.pdfviewer.test:attr/layout_constraintTop_toTopOf = 0x7f030273
com.github.barteksc.pdfviewer.test:style/Animation.Material3.SideSheetDialog.Right = 0x7f0f0009
com.github.barteksc.pdfviewer.test:color/m3_sys_color_light_on_background = 0x7f0501bb
com.github.barteksc.pdfviewer.test:attr/backgroundInsetBottom = 0x7f030045
com.github.barteksc.pdfviewer.test:styleable/TabLayout = 0x7f100084
com.github.barteksc.pdfviewer.test:color/abc_primary_text_material_dark = 0x7f05000b
com.github.barteksc.pdfviewer.test:string/mtrl_picker_announce_current_selection_none = 0x7f0e006f
com.github.barteksc.pdfviewer.test:layout/mtrl_picker_dialog = 0x7f0b0054
com.github.barteksc.pdfviewer.test:attr/trackCornerRadius = 0x7f030458
com.github.barteksc.pdfviewer.test:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.github.barteksc.pdfviewer.test:color/m3_tabs_icon_color = 0x7f0501e3
com.github.barteksc.pdfviewer.test:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.github.barteksc.pdfviewer.test:styleable/ScrimInsetsFrameLayout = 0x7f100071
com.github.barteksc.pdfviewer.test:dimen/mtrl_btn_pressed_z = 0x7f060262
com.github.barteksc.pdfviewer.test:style/TextAppearance.Material3.HeadlineSmall = 0x7f0f01ec
com.github.barteksc.pdfviewer.test:dimen/m3_comp_input_chip_container_height = 0x7f060132
com.github.barteksc.pdfviewer.test:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f0211
com.github.barteksc.pdfviewer.test:style/Animation.Design.BottomSheetDialog = 0x7f0f0005
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dark_on_surface = 0x7f050158
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant70 = 0x7f050116
com.github.barteksc.pdfviewer.test:attr/contentPaddingBottom = 0x7f030127
com.github.barteksc.pdfviewer.test:dimen/mtrl_slider_halo_radius = 0x7f0602de
com.github.barteksc.pdfviewer.test:attr/constraintSet = 0x7f030119
com.github.barteksc.pdfviewer.test:attr/textInputFilledStyle = 0x7f030416
com.github.barteksc.pdfviewer.test:dimen/mtrl_navigation_rail_text_size = 0x7f0602cc
com.github.barteksc.pdfviewer.test:attr/ratingBarStyleIndicator = 0x7f030359
com.github.barteksc.pdfviewer.test:id/fitToContents = 0x7f0800a6
com.github.barteksc.pdfviewer.test:attr/actionBarSplitStyle = 0x7f030004
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral_variant60 = 0x7f050115
com.github.barteksc.pdfviewer.test:id/accessibility_custom_action_15 = 0x7f080017
com.github.barteksc.pdfviewer.test:style/Widget.Material3.MaterialTimePicker.Button = 0x7f0f03ab
com.github.barteksc.pdfviewer.test:attr/shapeAppearanceMediumComponent = 0x7f03037d
com.github.barteksc.pdfviewer.test:layout/notification_template_part_chronometer = 0x7f0b0063
com.github.barteksc.pdfviewer.test:attr/actionModeBackground = 0x7f030010
com.github.barteksc.pdfviewer.test:layout/material_clock_display_divider = 0x7f0b0033
com.github.barteksc.pdfviewer.test:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.github.barteksc.pdfviewer.test:integer/mtrl_btn_anim_duration_ms = 0x7f09002f
com.github.barteksc.pdfviewer.test:integer/material_motion_duration_medium_2 = 0x7f090029
com.github.barteksc.pdfviewer.test:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600fc
com.github.barteksc.pdfviewer.test:attr/foregroundInsidePadding = 0x7f0301ee
com.github.barteksc.pdfviewer.test:anim/design_bottom_sheet_slide_out = 0x7f010019
com.github.barteksc.pdfviewer.test:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070011
com.github.barteksc.pdfviewer.test:attr/layout_insetEdge = 0x7f030284
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f0f0047
com.github.barteksc.pdfviewer.test:anim/abc_popup_enter = 0x7f010003
com.github.barteksc.pdfviewer.test:attr/helperTextTextColor = 0x7f0301fa
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501a7
com.github.barteksc.pdfviewer.test:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.github.barteksc.pdfviewer.test:drawable/mtrl_ic_error = 0x7f0700c5
com.github.barteksc.pdfviewer.test:dimen/design_fab_translation_z_hovered_focused = 0x7f060073
com.github.barteksc.pdfviewer.test:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.github.barteksc.pdfviewer.test:attr/iconEndPadding = 0x7f03020a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060159
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0068
com.github.barteksc.pdfviewer.test:attr/behavior_significantVelocityThreshold = 0x7f03006d
com.github.barteksc.pdfviewer.test:string/mtrl_switch_thumb_path_name = 0x7f0e0093
com.github.barteksc.pdfviewer.test:string/material_minute_suffix = 0x7f0e004c
com.github.barteksc.pdfviewer.test:color/m3_button_foreground_color_selector = 0x7f050064
com.github.barteksc.pdfviewer.test:id/fitEnd = 0x7f0800a4
com.github.barteksc.pdfviewer.test:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e
com.github.barteksc.pdfviewer.test:color/design_dark_default_color_on_primary = 0x7f050035
com.github.barteksc.pdfviewer.test:color/mtrl_btn_text_btn_bg_color_selector = 0x7f05029b
com.github.barteksc.pdfviewer.test:color/m3_tabs_icon_color_secondary = 0x7f0501e4
com.github.barteksc.pdfviewer.test:string/m3_ref_typeface_plain_regular = 0x7f0e003a
com.github.barteksc.pdfviewer.test:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007c
com.github.barteksc.pdfviewer.test:attr/circleRadius = 0x7f0300c0
com.github.barteksc.pdfviewer.test:attr/hintTextAppearance = 0x7f030202
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500e6
com.github.barteksc.pdfviewer.test:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b0043
com.github.barteksc.pdfviewer.test:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f060215
com.github.barteksc.pdfviewer.test:attr/chipIconEnabled = 0x7f0300b1
com.github.barteksc.pdfviewer.test:color/material_personalized_color_surface = 0x7f050278
com.github.barteksc.pdfviewer.test:attr/bottomInsetScrimEnabled = 0x7f030072
com.github.barteksc.pdfviewer.test:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bc
com.github.barteksc.pdfviewer.test:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.github.barteksc.pdfviewer.test:attr/layoutDescription = 0x7f030249
com.github.barteksc.pdfviewer.test:attr/minHideDelay = 0x7f0302e6
com.github.barteksc.pdfviewer.test:attr/actionModeShareDrawable = 0x7f03001a
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_container_width = 0x7f06014a
com.github.barteksc.pdfviewer.test:attr/itemVerticalPadding = 0x7f03023d
com.github.barteksc.pdfviewer.test:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f05008f
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonSecondaryStyle = 0x7f0301c4
com.github.barteksc.pdfviewer.test:drawable/m3_avd_show_password = 0x7f07009e
com.github.barteksc.pdfviewer.test:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f0f025c
com.github.barteksc.pdfviewer.test:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0129
com.github.barteksc.pdfviewer.test:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.github.barteksc.pdfviewer.test:attr/roundPercent = 0x7f030364
com.github.barteksc.pdfviewer.test:attr/layout_constraintStart_toEndOf = 0x7f03026e
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_neutral17 = 0x7f0500fa
com.github.barteksc.pdfviewer.test:attr/centerIfNoTextEnabled = 0x7f03009b
com.github.barteksc.pdfviewer.test:string/mtrl_switch_thumb_group_name = 0x7f0e0090
com.github.barteksc.pdfviewer.test:id/customPanel = 0x7f080075
com.github.barteksc.pdfviewer.test:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.github.barteksc.pdfviewer.test:dimen/design_snackbar_padding_horizontal = 0x7f060085
com.github.barteksc.pdfviewer.test:animator/fragment_open_enter = 0x7f020007
com.github.barteksc.pdfviewer.test:color/cardview_shadow_end_color = 0x7f05002d
com.github.barteksc.pdfviewer.test:attr/collapsedTitleTextAppearance = 0x7f0300d3
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3 = 0x7f0f027b
com.github.barteksc.pdfviewer.test:layout/design_layout_snackbar_include = 0x7f0b0020
com.github.barteksc.pdfviewer.test:attr/limitBoundsTo = 0x7f03028d
com.github.barteksc.pdfviewer.test:attr/actionBarPopupTheme = 0x7f030002
com.github.barteksc.pdfviewer.test:styleable/LinearLayoutCompat_Layout = 0x7f100046
com.github.barteksc.pdfviewer.test:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f0f0388
com.github.barteksc.pdfviewer.test:color/mtrl_btn_bg_color_selector = 0x7f050298
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f0f02a9
com.github.barteksc.pdfviewer.test:string/material_timepicker_am = 0x7f0e0055
com.github.barteksc.pdfviewer.test:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.github.barteksc.pdfviewer.test:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f004c
com.github.barteksc.pdfviewer.test:attr/textInputOutlinedDenseStyle = 0x7f030418
com.github.barteksc.pdfviewer.test:id/action_bar = 0x7f080030
com.github.barteksc.pdfviewer.test:drawable/abc_list_pressed_holo_dark = 0x7f070050
com.github.barteksc.pdfviewer.test:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012b
com.github.barteksc.pdfviewer.test:attr/defaultState = 0x7f030159
com.github.barteksc.pdfviewer.test:id/mtrl_picker_fullscreen = 0x7f080102
com.github.barteksc.pdfviewer.test:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.github.barteksc.pdfviewer.test:dimen/notification_content_margin_start = 0x7f060307
com.github.barteksc.pdfviewer.test:dimen/m3_navigation_rail_item_padding_top = 0x7f0601cb
com.github.barteksc.pdfviewer.test:attr/paddingLeftSystemWindowInsets = 0x7f03032e
com.github.barteksc.pdfviewer.test:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301c1
com.github.barteksc.pdfviewer.test:attr/materialIconButtonStyle = 0x7f0302cc
com.github.barteksc.pdfviewer.test:attr/region_widthMoreThan = 0x7f03035f
com.github.barteksc.pdfviewer.test:dimen/m3_fab_corner_size = 0x7f0601b0
com.github.barteksc.pdfviewer.test:attr/indeterminateAnimationType = 0x7f030213
com.github.barteksc.pdfviewer.test:attr/windowMinWidthMinor = 0x7f030480
com.github.barteksc.pdfviewer.test:attr/colorSurfaceContainerHighest = 0x7f03010c
com.github.barteksc.pdfviewer.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f0f02d2
com.github.barteksc.pdfviewer.test:color/mtrl_chip_surface_color = 0x7f0502a6
com.github.barteksc.pdfviewer.test:attr/expandedHintEnabled = 0x7f03019e
com.github.barteksc.pdfviewer.test:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601e1
com.github.barteksc.pdfviewer.test:style/Widget.AppCompat.Spinner = 0x7f0f0326
com.github.barteksc.pdfviewer.test:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f0145
com.github.barteksc.pdfviewer.test:attr/dragThreshold = 0x7f03016b
com.github.barteksc.pdfviewer.test:attr/tabStyle = 0x7f0303e3
com.github.barteksc.pdfviewer.test:attr/rangeFillColor = 0x7f030357
com.github.barteksc.pdfviewer.test:attr/drawerLayoutStyle = 0x7f030178
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_error70 = 0x7f0500f1
com.github.barteksc.pdfviewer.test:dimen/material_clock_display_height = 0x7f06021b
com.github.barteksc.pdfviewer.test:color/material_dynamic_tertiary60 = 0x7f05023e
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500e1
com.github.barteksc.pdfviewer.test:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.github.barteksc.pdfviewer.test:anim/abc_popup_exit = 0x7f010004
com.github.barteksc.pdfviewer.test:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060176
com.github.barteksc.pdfviewer.test:dimen/m3_card_hovered_z = 0x7f0600eb
com.github.barteksc.pdfviewer.test:color/m3_appbar_overlay_color = 0x7f05005f
com.github.barteksc.pdfviewer.test:attr/percentY = 0x7f030341
com.github.barteksc.pdfviewer.test:attr/colorButtonNormal = 0x7f0300dd
com.github.barteksc.pdfviewer.test:attr/contentPaddingRight = 0x7f03012a
com.github.barteksc.pdfviewer.test:styleable/MaterialButton = 0x7f10004c
com.github.barteksc.pdfviewer.test:attr/motionDurationLong4 = 0x7f0302f8
com.github.barteksc.pdfviewer.test:attr/colorBackgroundFloating = 0x7f0300dc
com.github.barteksc.pdfviewer.test:dimen/mtrl_textinput_start_icon_margin_end = 0x7f0602fc
com.github.barteksc.pdfviewer.test:color/m3_ref_palette_dynamic_primary10 = 0x7f0500c3
com.github.barteksc.pdfviewer.test:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e9
com.github.barteksc.pdfviewer.test:attr/paddingStart = 0x7f030330
com.github.barteksc.pdfviewer.test:dimen/material_time_picker_minimum_screen_width = 0x7f06023d
com.github.barteksc.pdfviewer.test:id/action_bar_spinner = 0x7f080034
com.github.barteksc.pdfviewer.test:macro/m3_comp_outlined_card_outline_color = 0x7f0c00b0
com.github.barteksc.pdfviewer.test:attr/materialCalendarHeaderToggleButton = 0x7f0302ba
com.github.barteksc.pdfviewer.test:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f01a8
com.github.barteksc.pdfviewer.test:attr/boxBackgroundColor = 0x7f030077
com.github.barteksc.pdfviewer.test:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f0f0044
com.github.barteksc.pdfviewer.test:attr/badgeGravity = 0x7f03004e
com.github.barteksc.pdfviewer.test:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060148
com.github.barteksc.pdfviewer.test:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501b4

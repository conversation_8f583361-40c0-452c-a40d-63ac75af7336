/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/work_space/PhotoCC_android/nativelib/src/main/cpp \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=29 \
  -DANDROID_PLATFORM=android-29 \
  -DANDROID_ABI=x86 \
  -DCMAKE_ANDROID_ARCH_ABI=x86 \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \
  -DC<PERSON>KE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx/Debug/2qk593ug/obj/x86 \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx/Debug/2qk593ug/obj/x86 \
  -DCMAKE_BUILD_TYPE=Debug \
  -B/Users/<USER>/work_space/PhotoCC_android/nativelib/.cxx/Debug/2qk593ug/x86 \
  -GNinja

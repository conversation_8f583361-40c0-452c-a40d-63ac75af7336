{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/.cxx/Debug/2qk593ug/x86", "soFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx/Debug/2qk593ug/obj/x86", "soRepublishFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cmake/debug/obj/x86", "abiPlatformVersion": 29, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [""], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/.cxx", "intermediatesBaseFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates", "intermediatesFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx", "gradleModulePathName": ":nativelib", "moduleRootFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib", "moduleBuildFile": "/Users/<USER>/work_space/PhotoCC_android/nativelib/build.gradle.kts", "makeFile": "/Users/<USER>/work_space/PhotoCC_android/nativelib/src/main/cpp/CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "ndkFolderBeforeSymLinking": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake", "cmake": {"cmakeExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "arm64-v8a": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "x86": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "x86_64": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/Users/<USER>/work_space/PhotoCC_android", "sdkFolder": "/Users/<USER>/Library/Android/sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/.cxx/Debug/2qk593ug/prefab/x86", "isActiveAbi": true, "fullConfigurationHash": "2qk593ug341s1i4h351v624d645h2r176i3g1b6046c5f3a695j22t10193", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.6.0.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/nativelib/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=29\n-DANDROID_PLATFORM=android-29\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/nativelib/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/nativelib/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/nativelib/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-H/Users/<USER>/work_space/PhotoCC_android/nativelib/src/main/cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=29", "-DANDROID_PLATFORM=android-29", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx/Debug/2qk593ug/obj/x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx/Debug/2qk593ug/obj/x86", "-DCMAKE_BUILD_TYPE=Debug", "-B/Users/<USER>/work_space/PhotoCC_android/nativelib/.cxx/Debug/2qk593ug/x86", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "/Users/<USER>/work_space/PhotoCC_android/nativelib/build/intermediates/cxx/Debug/2qk593ug"}
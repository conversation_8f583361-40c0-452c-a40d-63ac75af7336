package com.wtb.photocc_android.preview

import android.net.Uri
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView

/**
 * PDF 阅读器界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PdfReaderScreen(
    documentItem: DocumentItem,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var previewState by remember { mutableStateOf<DocumentPreviewState>(DocumentPreviewState.Loading) }
    var showSettings by remember { mutableStateOf(false) }
    var config by remember { mutableStateOf(DocumentPreviewConfig()) }

    val context = LocalContext.current

    Column(modifier = modifier.fillMaxSize()) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Column {
                    Text(
                        text = documentItem.name,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    if (totalPages > 0) {
                        Text(
                            text = "${currentPage + 1} / $totalPages",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { showSettings = true }) {
                    Icon(Icons.Default.Settings, contentDescription = "设置")
                }
            }
        )

        // PDF 内容
        Box(modifier = Modifier.fillMaxSize()) {
            when (previewState) {
                is DocumentPreviewState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载 PDF...")
                        }
                    }
                }
                
                is DocumentPreviewState.Error -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "加载失败",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = previewState.message,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Button(onClick = {
                                previewState = DocumentPreviewState.Loading
                            }) {
                                Text("重试")
                            }
                        }
                    }
                }
                
                is DocumentPreviewState.Ready -> {
                    AndroidView(
                        factory = { context ->
                            PDFView(context, null).apply {
                                loadPdfFromUri(
                                    context = context,
                                    uri = documentItem.getUri(),
                                    config = config,
                                    onLoadComplete = { pages ->
                                        totalPages = pages
                                        previewState = DocumentPreviewState.Ready
                                    },
                                    onPageChange = { page, _ ->
                                        currentPage = page
                                    },
                                    onError = { error ->
                                        previewState = DocumentPreviewState.Error(
                                            error.message ?: "未知错误"
                                        )
                                    }
                                )
                            }
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }

    // 设置对话框
    if (showSettings) {
        PdfSettingsDialog(
            config = config,
            onConfigChange = { newConfig ->
                config = newConfig
                // 重新加载 PDF 以应用新设置
                previewState = DocumentPreviewState.Loading
            },
            onDismiss = { showSettings = false }
        )
    }

    // 初始加载
    LaunchedEffect(documentItem.uri) {
        previewState = DocumentPreviewState.Loading
    }
}

/**
 * 扩展函数：从 URI 加载 PDF
 */
private fun PDFView.loadPdfFromUri(
    context: Context,
    uri: Uri,
    config: DocumentPreviewConfig,
    onLoadComplete: (Int) -> Unit,
    onPageChange: (Int, Int) -> Unit,
    onError: (Throwable) -> Unit
) {
    try {
        val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
        if (inputStream != null) {
            fromStream(inputStream)
                .enableSwipe(config.enableSwipe)
                .swipeHorizontal(false)
                .enableDoubletap(config.enableZoom)
                .defaultPage(0)
                .enableAnnotationRendering(false)
                .password(null)
                .scrollHandle(DefaultScrollHandle(context))
                .enableAntialiasing(true)
                .spacing(if (config.autoSpacing) 10 else 0)
                .pageFling(config.pageFling)
                .pageSnap(config.pageSnap)
                .nightMode(config.nightMode)
                .onLoad(object : OnLoadCompleteListener {
                    override fun loadComplete(nbPages: Int) {
                        onLoadComplete(nbPages)
                    }
                })
                .onPageChange(object : OnPageChangeListener {
                    override fun onPageChanged(page: Int, pageCount: Int) {
                        onPageChange(page, pageCount)
                    }
                })
                .onError(object : OnPageErrorListener {
                    override fun onPageError(page: Int, t: Throwable?) {
                        onError(t ?: Exception("页面加载错误"))
                    }
                })
                .load()
        } else {
            onError(Exception("无法打开文件"))
        }
    } catch (e: Exception) {
        onError(e)
    }
}

/**
 * PDF 设置对话框
 */
@Composable
private fun PdfSettingsDialog(
    config: DocumentPreviewConfig,
    onConfigChange: (DocumentPreviewConfig) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("PDF 阅读设置") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("启用缩放")
                    Switch(
                        checked = config.enableZoom,
                        onCheckedChange = { 
                            onConfigChange(config.copy(enableZoom = it))
                        }
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("启用滑动")
                    Switch(
                        checked = config.enableSwipe,
                        onCheckedChange = { 
                            onConfigChange(config.copy(enableSwipe = it))
                        }
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("夜间模式")
                    Switch(
                        checked = config.nightMode,
                        onCheckedChange = { 
                            onConfigChange(config.copy(nightMode = it))
                        }
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("自动间距")
                    Switch(
                        checked = config.autoSpacing,
                        onCheckedChange = { 
                            onConfigChange(config.copy(autoSpacing = it))
                        }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

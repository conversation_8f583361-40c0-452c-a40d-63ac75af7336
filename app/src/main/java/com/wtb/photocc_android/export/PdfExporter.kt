package com.wtb.photocc_android.export

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.element.Image
import com.itextpdf.layout.element.Table
import com.itextpdf.layout.properties.UnitValue
import com.itextpdf.io.image.ImageDataFactory
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportTask
import com.wtb.photocc_android.data.export.ExportStatus
import com.wtb.photocc_android.data.export.LayoutConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.OutputStream

class PdfExporter(private val context: Context) {

    companion object {
        private const val TAG = "PdfExporter"
    }

    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        Log.d(TAG, "开始PDF导出，图片数量: ${task.images.size}")

        var pdfWriter: PdfWriter? = null
        var pdfDocument: PdfDocument? = null
        var document: Document? = null

        try {
            val config = task.config
            val layoutConfig = config.layoutConfig
                ?: return@withContext Result.failure(Exception("PDF导出需要排版配置"))

            Log.d(TAG, "PDF配置: 质量=${config.quality}, 最大尺寸=${config.maxWidth}x${config.maxHeight}")
            Log.d(TAG, "排版配置: 模板=${layoutConfig.template}, 列数=${layoutConfig.getActualColumns()}")

            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "准备PDF文档..."))

            pdfWriter = PdfWriter(outputStream)
            pdfDocument = PdfDocument(pdfWriter)
            document = Document(pdfDocument)

            Log.d(TAG, "PDF文档已创建")
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 10f, "处理图片..."))

            // 根据排版配置创建表格
            val columns = layoutConfig.getActualColumns()
            Log.d(TAG, "创建PDF表格，列数: $columns")
            val table = Table(UnitValue.createPercentArray(columns))
            table.setWidth(UnitValue.createPercentValue(100f))

            val images = task.images
            var processedCount = 0
            var successCount = 0
            var failCount = 0

            for (i in images.indices step columns) {
                for (j in 0 until columns) {
                    val imageIndex = i + j

                    if (imageIndex < images.size) {
                        val imageItem = images[imageIndex]
                        Log.d(TAG, "处理图片 ${imageIndex + 1}/${images.size}: ${imageItem.name}")

                        val bitmap = loadAndProcessBitmap(imageItem, config)

                        if (bitmap != null) {
                            try {
                                val imageBytes = bitmapToByteArray(bitmap, config.quality)
                                val imageData = ImageDataFactory.create(imageBytes)
                                val pdfImage = Image(imageData)

                                // 设置图片大小
                                val cellWidth = (pdfDocument.defaultPageSize.width - 72) / columns // 72是页边距
                                pdfImage.setWidth(cellWidth)
                                pdfImage.setAutoScale(true)

                                table.addCell(pdfImage)
                                bitmap.recycle()
                                successCount++
                                Log.d(TAG, "成功添加图片到PDF: ${imageItem.name}")
                            } catch (e: Exception) {
                                Log.e(TAG, "添加图片到PDF失败: ${imageItem.name}", e)
                                table.addCell("") // 空单元格
                                failCount++
                            }
                        } else {
                            Log.w(TAG, "无法加载图片: ${imageItem.name}")
                            table.addCell("") // 空单元格
                            failCount++
                        }
                    } else {
                        table.addCell("") // 空单元格
                    }

                    processedCount++
                    val progress = 10f + (processedCount.toFloat() / (images.size + columns - 1)) * 80f
                    onProgress(task.updateStatus(
                        ExportStatus.PROCESSING,
                        progress,
                        "处理图片 $processedCount/${images.size}..."
                    ))
                }
            }

            Log.d(TAG, "图片处理完成: 成功=$successCount, 失败=$failCount")

            Log.d(TAG, "添加表格到PDF文档")
            document.add(table as com.itextpdf.layout.element.IBlockElement)

            // 安全关闭PDF文档
            try {
                document.close()
                Log.d(TAG, "PDF文档已关闭")
            } catch (e: Exception) {
                Log.e(TAG, "关闭PDF文档时出错", e)
                throw e
            }

            onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "PDF导出完成"))
            Result.success("PDF导出成功，包含 $successCount 张图片")

        } catch (e: Exception) {
            Log.e(TAG, "PDF导出失败", e)

            // 尝试清理资源
            try {
                document?.close()
            } catch (cleanupException: Exception) {
                Log.w(TAG, "清理PDF资源时出错", cleanupException)
            }

            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private suspend fun loadAndProcessBitmap(
        imageItem: ImageItem,
        config: ExportConfig
    ): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            Log.d(TAG, "加载图片: ${imageItem.name}, URI: $uri")

            val inputStream = context.contentResolver.openInputStream(uri)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (originalBitmap != null) {
                // 调整大小
                val scaledBitmap = scaleBitmap(originalBitmap, config.maxWidth, config.maxHeight)
                if (scaledBitmap != originalBitmap) {
                    originalBitmap.recycle()
                }
                scaledBitmap
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun scaleBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleX = maxWidth.toFloat() / width
        val scaleY = maxHeight.toFloat() / height
        val scale = minOf(scaleX, scaleY)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    private fun bitmapToByteArray(bitmap: Bitmap, quality: Int): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
        return outputStream.toByteArray()
    }
}

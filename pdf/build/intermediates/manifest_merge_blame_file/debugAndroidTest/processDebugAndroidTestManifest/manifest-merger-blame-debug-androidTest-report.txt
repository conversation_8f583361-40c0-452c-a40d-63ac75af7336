1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.github.barteksc.pdfviewer.test" >
4
5    <uses-sdk
5-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:5:5-74
6        android:minSdkVersion="29"
6-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:5:15-41
7        android:targetSdkVersion="29" />
7-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:5:42-71
8
9    <instrumentation
9-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:11:5-15:84
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:11:22-76
11        android:functionalTest="false"
11-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:14:22-52
12        android:handleProfiling="false"
12-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:13:22-53
13        android:label="Tests for com.github.barteksc.pdfviewer.test"
13-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:15:22-82
14        android:targetPackage="com.github.barteksc.pdfviewer.test" />
14-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:12:22-80
15
16    <queries>
16-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:24:5-28:15
17        <package android:name="androidx.test.orchestrator" />
17-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:25:9-62
17-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:25:18-59
18        <package android:name="androidx.test.services" />
18-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:26:9-58
18-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:26:18-55
19        <package android:name="com.google.android.apps.common.testing.services" />
19-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:27:9-83
19-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/bddd313a7feaf3c2866e7df7eb4fcef9/transformed/runner-1.5.2/AndroidManifest.xml:27:18-80
20    </queries>
21
22    <uses-permission android:name="android.permission.REORDER_TASKS" />
22-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:24:5-72
22-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:24:22-69
23
24    <permission
24-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/9ac79d54b78cf7b8633584d54f14f0af/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
25        android:name="com.github.barteksc.pdfviewer.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/9ac79d54b78cf7b8633584d54f14f0af/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/9ac79d54b78cf7b8633584d54f14f0af/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.github.barteksc.pdfviewer.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/9ac79d54b78cf7b8633584d54f14f0af/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/9ac79d54b78cf7b8633584d54f14f0af/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
29
30    <application
30-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:7:5-9:19
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/9ac79d54b78cf7b8633584d54f14f0af/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
32        android:debuggable="true"
33        android:extractNativeLibs="false" >
34        <uses-library android:name="android.test.runner" />
34-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:8:9-60
34-->/Users/<USER>/work_space/PhotoCC_android/pdf/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest4488886120482504953.xml:8:23-57
35
36        <activity
36-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:27:9-34:20
37            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
37-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:28:13-99
38            android:exported="true"
38-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:29:13-36
39            android:theme="@style/WhiteBackgroundTheme" >
39-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:30:13-56
40            <intent-filter android:priority="-100" >
40-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:31:13-33:29
40-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:31:28-51
41                <category android:name="android.intent.category.LAUNCHER" />
41-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:32:17-77
41-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:32:27-74
42            </intent-filter>
43        </activity>
44        <activity
44-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:35:9-42:20
45            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
45-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:36:13-95
46            android:exported="true"
46-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:37:13-36
47            android:theme="@style/WhiteBackgroundTheme" >
47-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:38:13-56
48            <intent-filter android:priority="-100" >
48-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:31:13-33:29
48-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:31:28-51
49                <category android:name="android.intent.category.LAUNCHER" />
49-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:32:17-77
49-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:32:27-74
50            </intent-filter>
51        </activity>
52        <activity
52-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:43:9-50:20
53            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
53-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:44:13-103
54            android:exported="true"
54-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:45:13-36
55            android:theme="@style/WhiteBackgroundDialogTheme" >
55-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:46:13-62
56            <intent-filter android:priority="-100" >
56-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:31:13-33:29
56-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:31:28-51
57                <category android:name="android.intent.category.LAUNCHER" />
57-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:32:17-77
57-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/53e1183f757b21edd8b683097a50b129/transformed/core-1.5.0/AndroidManifest.xml:32:27-74
58            </intent-filter>
59        </activity>
60
61        <provider
61-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
62            android:name="androidx.startup.InitializationProvider"
62-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
63            android:authorities="com.github.barteksc.pdfviewer.test.androidx-startup"
63-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
64            android:exported="false" >
64-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
65            <meta-data
65-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
66                android:name="androidx.emoji2.text.EmojiCompatInitializer"
66-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
67                android:value="androidx.startup" />
67-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/5b323ce31d76a4f67f5cd93b366a6f25/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/46ad3011db6ee1b566c133166395d41f/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
69                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
69-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/46ad3011db6ee1b566c133166395d41f/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
70                android:value="androidx.startup" />
70-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/46ad3011db6ee1b566c133166395d41f/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
72                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
73                android:value="androidx.startup" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
74        </provider>
75
76        <receiver
76-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
77            android:name="androidx.profileinstaller.ProfileInstallReceiver"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
78            android:directBootAware="false"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
79            android:enabled="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
80            android:exported="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
81            android:permission="android.permission.DUMP" >
81-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
83                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
83-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
86                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
86-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
89                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
89-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
92                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
92-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e2c807562cbe326206924d34674603b/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
93            </intent-filter>
94        </receiver>
95    </application>
96
97</manifest>
